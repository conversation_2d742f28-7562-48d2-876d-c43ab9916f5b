# 生产环境配置
# Production Environment Configuration

# ================================
# 应用基础配置
# ================================
APP_NAME=AI Chat LangChain
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=WARNING

# ================================
# 服务器配置
# ================================
SERVER_HOST=0.0.0.0
SERVER_PORT=8001

# ================================
# LLM配置 (生产环境)
# ================================
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=sk-prod-api-key-replace-me
LOCAL_LLM_MODEL_URL=http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3-235B-A22B
LOCAL_LLM_TEMPERATURE=0.3
LOCAL_LLM_TIMEOUT=300

# ================================
# 数据库配置 (生产环境)
# ================================
MYSQL_HOST=prod-mysql-cluster
MYSQL_PORT=3306
MYSQL_USER=prod_user
MYSQL_PASSWORD=prod_password_replace_me
MYSQL_DATABASE=analysis_prod
MYSQL_CHARSET=utf8mb4

# ================================
# LangSmith配置 (生产环境)
# ================================
LANGCHAIN_TRACING_V2=false
LANGCHAIN_PROJECT=ai-chat-langchain-prod

# ================================
# dify知识库查询服务配置 (生产环境)
# ================================
KNOWLEDGE_SEARCH_API_URL=http://*************/v1/workflows/run
KNOWLEDGE_SEARCH_API_KEY=app-yBG3HXjw9NiGcxXzJ4QmHEFD

# ================================
# SQL生成服务VANNA配置 (生产环境)
# ================================
SQL_GENERATOR_API_URL=http://**************:5000/api/v0/generate_sql
SQL_GENERATOR_API_KEY=vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1

# ================================
# 省份数据库映射配置 (生产环境 - 完整配置)
# ================================
PROVINCE_DATABASE_MAPPING=NX:nxtidb:宁夏,GS:gstidb:甘肃,HE:hetidb:河北,GZ:gztidb:贵州,XXL:xxltidb:特殊区域,JT:qgtidb:总部,BJ:bjtidb:北京,TJ:tjtidb:天津,SX:sxtidb:山西,NM:nmtidb:内蒙古,LN:lntidb:辽宁,JL:jltidb:吉林,HL:hltidb:黑龙江,SH:shtidb:上海,JS:jstidb:江苏,ZJ:zjtidb:浙江,AH:ahtidb:安徽,FJ:fjtidb:福建,JX:jxtidb:江西,SD:sdtidb:山东,HA:hatidb:河南,HB:hbtidb:湖北,HN:hntidb:湖南,GD:gdtidb:广东,GX:gxtidb:广西,HI:hitidb:海南,CQ:cqtidb:重庆,SC:sctidb:四川,YN:yntidb:云南,XZ:xztidb:西藏,SN:sntidb:陕西,QH:qhtidb:青海,XJ:xjtidb:新疆

# ================================
# 远程Embedding模型配置 (生产环境)
# ================================
# 是否启用远程embedding服务
REMOTE_EMBEDDING_ENABLED=true
# 远程embedding服务URL
REMOTE_EMBEDDING_URL=http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
# 远程embedding服务API密钥
REMOTE_EMBEDDING_API_KEY=sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ
# 远程embedding模型名称
REMOTE_EMBEDDING_MODEL=bge-large-zh-v1.5
