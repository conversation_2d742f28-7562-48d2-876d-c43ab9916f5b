# 生产环境配置
# Production Environment Configuration

# ================================
# 应用基础配置
# ================================
APP_NAME=AI Chat LangChain
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=WARNING

# ================================
# 服务器配置
# ================================
SERVER_HOST=0.0.0.0
SERVER_PORT=8001

# ================================
# LLM配置 (生产环境)
# ================================
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=sk-prod-api-key-replace-me
LOCAL_LLM_MODEL_URL=http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3-235B-A22B
LOCAL_LLM_TEMPERATURE=0.3
LOCAL_LLM_TIMEOUT=300

# ================================
# 数据库配置 (生产环境)
# ================================
MYSQL_HOST=prod-mysql-cluster
MYSQL_PORT=3306
MYSQL_USER=prod_user
MYSQL_PASSWORD=prod_password_replace_me
MYSQL_DATABASE=analysis_prod
MYSQL_CHARSET=utf8mb4

# ================================
# LangSmith配置 (生产环境)
# ================================
LANGCHAIN_TRACING_V2=false
LANGCHAIN_PROJECT=ai-chat-langchain-prod

# ================================
# dify知识库查询服务配置 (生产环境)
# ================================
KNOWLEDGE_SEARCH_API_URL=http://*************/v1/workflows/run
KNOWLEDGE_SEARCH_API_KEY=app-yBG3HXjw9NiGcxXzJ4QmHEFD

# ================================
# SQL生成服务VANNA配置 (生产环境)
# ================================
SQL_GENERATOR_API_URL=http://**************:5000/api/v0/generate_sql
SQL_GENERATOR_API_KEY=vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1

# ================================
# 省份数据库映射配置 (生产环境 - 完整配置)
# ================================
PROVINCE_DATABASE_MAPPING=NX:analysis_nx:宁夏,GS:analysis_gs:甘肃,HE:analysis_he:河北,GZ:analysis_gz:贵州,XXL:xxltidb:特殊区域,JT:analysis_qg:总部,BJ:analysis_bj:北京,TJ:analysis_tj:天津,SX:analysis_sx:山西,NM:analysis_nm:内蒙古,LN:analysis_ln:辽宁,JL:analysis_jl:吉林,HL:analysis_hl:黑龙江,SH:analysis_sh:上海,JS:analysis_js:江苏,ZJ:analysis_zj:浙江,AH:analysis_ah:安徽,FJ:analysis_fj:福建,JX:analysis_jx:江西,SD:analysis_sd:山东,HA:analysis_ha:河南,HB:analysis_hb:湖北,HN:analysis_hn:湖南,GD:analysis_gd:广东,GX:analysis_gx:广西,HI:analysis_hi:海南,CQ:analysis_cq:重庆,SC:analysis_sc:四川,YN:analysis_yn:云南,XZ:analysis_xz:西藏,SN:analysis_sn:陕西,QH:analysis_qh:青海,XJ:analysis_xj:新疆

# ================================
# 远程Embedding模型配置 (生产环境)
# ================================
# 是否启用远程embedding服务
REMOTE_EMBEDDING_ENABLED=true
# 远程embedding服务URL
REMOTE_EMBEDDING_URL=http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
# 远程embedding服务API密钥
REMOTE_EMBEDDING_API_KEY=sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ
# 远程embedding模型名称
REMOTE_EMBEDDING_MODEL=bge-large-zh-v1.5
