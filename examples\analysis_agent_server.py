"""
SQL Agent API 服务器
专门提供集成SQL查询功能的API服务，兼容Dify等平台接入
"""

import sys
import os
import time
import uuid
import json
import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Iterator
from datetime import datetime
from contextlib import asynccontextmanager
import concurrent.futures
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志记录
import os
from datetime import datetime
import logging.handlers

# 创建logs文件夹（如果不存在）
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
    print(f"📁 已创建日志文件夹: {log_dir}")

# 使用TimedRotatingFileHandler实现日期自动切换
def setup_daily_log():
    """设置每日自动切换的日志文件"""
    # 当前日期的日志文件名
    current_date = datetime.now().strftime('%Y%m%d')
    log_filename = os.path.join(log_dir, f"analysis_agent_{current_date}.log")
    
    # 创建定时切换的文件处理器
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_filename,
        when='midnight',       # 每天午夜切换
        interval=1,            # 每1天
        backupCount=30,        # 保留30天的日志
        encoding='utf-8',
        delay=False,
        utc=False
    )
    
    # 自定义日志文件命名格式
    file_handler.suffix = "%Y%m%d.log"
    file_handler.namer = lambda name: name.replace(".log", "").replace("analysis_agent_", "analysis_agent_") + ".log"
    
    return file_handler, log_filename

# 设置日志处理器
daily_file_handler, current_log_file = setup_daily_log()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        daily_file_handler
    ]
)

print(f"📝 当前日志文件: {current_log_file}")
print(f"🔄 日志将在每天午夜自动切换新文件")

# 设置Agent相关的日志记录器为INFO级别
logging.getLogger('src.agents.enhanced_tool_calling_agent').setLevel(logging.INFO)
logging.getLogger('src.tools').setLevel(logging.INFO)

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import uvicorn

from src.config.settings import settings
from src.core.llm_factory import LLMFactory
from src.tools import get_all_tools
from src.agents.enhanced_tool_calling_agent import EnhancedToolCallingAgent
from src.agents.province_aware_agent import ProvinceAwareAgent
from src.agents.vector_enhanced_agent import VectorEnhancedAgent
from src.api.prompt_management_api import router as prompt_router

# ===== 性能监控配置 =====
class PerformanceMonitor:
    """性能监控器"""
    def __init__(self):
        self.stats = {
            "total_requests": 0,
            "avg_response_time": 0,
            "tool_call_stats": {},
            "llm_call_stats": {"count": 0, "total_time": 0, "avg_time": 0}
        }
    
    def record_request(self, duration: float, tool_calls: int = 0, llm_calls: int = 0, llm_time: float = 0):
        """记录请求性能数据"""
        self.stats["total_requests"] += 1
        
        # 更新平均响应时间
        old_avg = self.stats["avg_response_time"]
        self.stats["avg_response_time"] = (old_avg * (self.stats["total_requests"] - 1) + duration) / self.stats["total_requests"]
        
        # 更新LLM调用统计
        self.stats["llm_call_stats"]["count"] += llm_calls
        self.stats["llm_call_stats"]["total_time"] += llm_time
        if self.stats["llm_call_stats"]["count"] > 0:
            self.stats["llm_call_stats"]["avg_time"] = self.stats["llm_call_stats"]["total_time"] / self.stats["llm_call_stats"]["count"]
    
    def get_stats(self):
        """获取性能统计"""
        return self.stats

# 全局性能监控器
performance_monitor = PerformanceMonitor()

# 在文件开头添加性能优化配置
PERFORMANCE_CONFIG = {
    "temperature": 0.1,  # 进一步降低温度提高响应速度
    "max_tokens": 3000,   # 减少输出长度，提高响应速度
    "timeout": settings.agent_timeout,       # 从环境变量读取超时时间
    "max_iterations": settings.agent_max_iterations,  # 从环境变量读取最大迭代次数
}


# ===== Pydantic 模型定义 =====

class ChatMessage(BaseModel):
    role: str = Field(..., description="消息角色: user, assistant, system")
    content: str = Field(..., description="消息内容")


class SqlQueryRequest(BaseModel):
    """SQL查询请求"""
    question: str = Field(..., description="自然语言问题")
    session_id: Optional[str] = Field(None, description="会话ID，用于保持对话上下文")
    database_type: Optional[str] = Field("mysql", description="数据库类型")
    include_sql: Optional[bool] = Field(True, description="是否在响应中包含生成的SQL")
    include_data: Optional[bool] = Field(True, description="是否在响应中包含查询数据")


class SqlQueryResponse(BaseModel):
    """SQL查询响应"""
    success: bool = Field(..., description="查询是否成功")
    answer: str = Field(..., description="自然语言回答")
    generated_sql: Optional[str] = Field(None, description="生成的SQL查询")
    data: Optional[List[Dict[str, Any]]] = Field(None, description="查询结果数据")
    row_count: Optional[int] = Field(None, description="返回行数")
    execution_time_ms: Optional[float] = Field(None, description="执行时间（毫秒）")
    error: Optional[str] = Field(None, description="错误信息")
    session_id: str = Field(..., description="会话ID")


class ChatCompletionRequest(BaseModel):
    """兼容OpenAI的聊天完成请求"""
    model: str = Field(..., description="模型名称")
    messages: List[ChatMessage] = Field(..., description="对话消息列表")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    temperature: Optional[float] = Field(0.7, description="生成温度", ge=0.0, le=2.0)
    stream: Optional[bool] = Field(False, description="是否流式输出")
    show_think_tags: Optional[bool] = Field(True, description="是否显示think标签（默认true显示）")


class ChatCompletionChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: str


class ChatCompletionUsage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: ChatCompletionUsage


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    service: str
    timestamp: str
    tools_available: int
    database_status: str
    error: Optional[str] = Field(None, description="错误信息")


# ===== FastAPI 应用设置 =====

_agent_executor: Optional[EnhancedToolCallingAgent] = None
_session_executors: Dict[str, EnhancedToolCallingAgent] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 启动Analysis Agent API服务器...")
    init_sql_agent()
    yield
    # 关闭时执行（如果有需要的清理工作）
    print("👋 Analysis Agent API服务器关闭")


app = FastAPI(
    title="Analysis Agent API 服务器",
    description="基于AI的数据分析Agent服务，支持SQL查询和知识库搜索，兼容Dify等平台接入",
    version="1.0.0",
    lifespan=lifespan
)

# 包含复杂提示词管理API路由
app.include_router(prompt_router)

# 挂载静态文件服务
app.mount("/static", StaticFiles(directory="src/web/static"), name="static")

# 添加管理界面路由
@app.get("/admin")
async def admin_page():
    """复杂提示词管理界面"""
    return FileResponse("src/web/static/prompt_management.html")

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# ===== 工具函数 =====

def get_current_timestamp() -> int:
    """获取当前时间戳"""
    return int(time.time())


def generate_completion_id() -> str:
    """生成聊天完成的唯一ID"""
    return f"sqlcmpl-{uuid.uuid4().hex[:24]}"


def estimate_tokens(text: str) -> int:
    """估算token数量"""
    chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
    english_words = len(text.split()) - chinese_chars
    return chinese_chars + english_words


def generate_think_tags(user_message: str, agent_response: str, tool_used: bool = False, tool_calls_history: list = None) -> str:
    """手动生成think标签来模拟AI的思考过程"""
    
    # 分析用户问题类型
    needs_sql = any(keyword in user_message.lower() for keyword in 
                  ["电费", "用水", "用电", "查询", "多少", "数据", "统计", "报表", "年", "月", "日", "度", "费用"])
    needs_knowledge = any(keyword in user_message.lower() for keyword in 
                        ["怎么", "如何", "为什么", "什么是", "计算", "原理", "方法", "规则", "解释"])
    
    think_content = []
    think_content.append("🤔 正在思考您的问题...")
    
    # 问题分析
    if needs_sql:
        think_content.append("\n\n📊 检测到数据查询需求:")
        think_content.append("- 用户想要查询具体的数据")
        think_content.append("- 需要调用SQL工具来获取数据")
        think_content.append("- 可能需要格式化展示结果")
    elif needs_knowledge:
        think_content.append("\n\n📚 检测到知识查询需求:")
        think_content.append("- 用户想要了解概念或原理")
        think_content.append("- 需要搜索知识库或基于已有知识回答")
        think_content.append("- 重点是解释清楚概念")
    else:
        think_content.append("\n\n💭 这是一个一般性问题:")
        think_content.append("- 用户可能是在对话交流")
        think_content.append("- 需要友好地回应")
    
    # 🆕 动态工具使用分析 - 基于实际执行历史
    if tool_used and tool_calls_history:
        think_content.append("\n\n🔧 工具调用分析:")
        
        # 统计各种工具的调用情况
        sql_calls = []
        knowledge_calls = []
        other_calls = []
        
        for i, tool_info in enumerate(tool_calls_history, 1):
            tool_name = tool_info["tool_call"]["tool_name"]
            success = tool_info["tool_result"]["success"]
            
            # 获取用时信息
            timing_info = tool_info.get("timing", {})
            tool_duration = timing_info.get("tool_duration", 0)
            
            if tool_name == "integrated_sql":
                sql_calls.append({
                    "step": i,
                    "success": success,
                    "duration": tool_duration,
                    "parameters": tool_info["tool_call"].get("parameters", {}),
                    "error": tool_info["tool_result"].get("error", "")
                })
            elif tool_name == "knowledge_search":
                knowledge_calls.append({
                    "step": i,
                    "success": success,
                    "duration": tool_duration,
                    "parameters": tool_info["tool_call"].get("parameters", {}),
                    "result": tool_info["tool_result"].get("result", {})
                })
            else:
                other_calls.append({
                    "step": i,
                    "tool_name": tool_name,
                    "success": success,
                    "duration": tool_duration
                })
        
        # 显示SQL工具调用情况
        if sql_calls:
            for sql_call in sql_calls:
                if sql_call["success"]:
                    think_content.append(f"- ✅ 步骤{sql_call['step']}: SQL工具成功执行，获得了数据 (用时: {sql_call['duration']:.2f}秒)")
                    think_content.append("  📊 从数据库获取了具体的数值和统计信息")
                else:
                    think_content.append(f"- ❌ 步骤{sql_call['step']}: SQL工具执行失败 (用时: {sql_call['duration']:.2f}秒)")
                    # 显示失败原因的简要说明
                    error_msg = sql_call["error"]
                    if "Unknown column" in error_msg:
                        think_content.append("  💡 检测到字段不存在问题，可能需要查询正确的表结构")
                    elif "syntax error" in error_msg.lower():
                        think_content.append("  💡 检测到SQL语法错误，需要优化查询语句")
                    else:
                        think_content.append(f"  ⚠️ 错误详情: {error_msg[:100]}...")
        
        # 显示知识库搜索情况
        if knowledge_calls:
            for knowledge_call in knowledge_calls:
                if knowledge_call["success"]:
                    result = knowledge_call["result"]
                    result_count = result.get("result_count", 0) if isinstance(result, dict) else 0
                    think_content.append(f"- ✅ 步骤{knowledge_call['step']}: 知识库搜索成功，找到了{result_count}条相关信息 (用时: {knowledge_call['duration']:.2f}秒)")
                    think_content.append("  📚 获取了业务逻辑和概念解释信息")
                else:
                    think_content.append(f"- ❌ 步骤{knowledge_call['step']}: 知识库搜索没有找到相关信息 (用时: {knowledge_call['duration']:.2f}秒)")
        
        # 显示其他工具调用情况
        if other_calls:
            for other_call in other_calls:
                status = "✅" if other_call["success"] else "❌"
                think_content.append(f"- {status} 步骤{other_call['step']}: {other_call['tool_name']}工具执行{'完成' if other_call['success'] else '失败'} (用时: {other_call['duration']:.2f}秒)")
        
        # 整合策略说明
        total_tools = len(tool_calls_history)
        successful_tools = sum(1 for tool_info in tool_calls_history if tool_info["tool_result"]["success"])
        
        if total_tools > 1:
            think_content.append(f"\n📝 多步骤执行: 共进行了{total_tools}步操作，其中{successful_tools}步成功")
            
            # 分析执行策略
            if sql_calls and knowledge_calls:
                think_content.append("- 🔄 策略: 先尝试数据查询，然后补充业务逻辑信息")
            elif len(sql_calls) > 1:
                think_content.append("- 🔄 策略: 多次SQL查询以获取完整数据")
            elif len(knowledge_calls) > 1:
                think_content.append("- 🔄 策略: 多次知识库搜索以获取全面信息")
        
    else:
        think_content.append("\n\n🧠 直接回答模式:")
        think_content.append("- 不需要使用工具")
        think_content.append("- 基于训练知识直接回答")
    
    # 回答策略
    think_content.append("\n\n📝 回答策略:")
    if tool_used:
        think_content.append("- 整合工具结果和分析")
        think_content.append("- 用清晰的格式展示信息") 
        think_content.append("- 确保用户容易理解")
        
        # 基于实际工具执行情况给出策略
        if tool_calls_history:
            failed_calls = [tool_info for tool_info in tool_calls_history if not tool_info["tool_result"]["success"]]
            if failed_calls:
                think_content.append("- 🔧 处理执行失败的工具调用，提供替代方案或说明")
    else:
        think_content.append("- 提供准确、有用的信息")
        think_content.append("- 保持友好的对话语调")
    
    think_content.append("\n\n✨ 正在整理最终答案...")
    
    think_text = "\n".join(think_content)
    
    # 组合think标签和实际回复
    return f"<think>\n{think_text}\n</think>\n\n{agent_response}"


def generate_think_tags_only(user_message: str, tool_used: bool = False, tool_calls_history: list = None) -> str:
    """生成只包含think标签的文本，包含完整的思考过程"""
    needs_sql = any(keyword in user_message.lower() for keyword in 
                  ["电费", "用水", "用电", "查询", "多少", "数据", "统计", "报表", "年", "月", "日", "度", "费用"])
    needs_knowledge = any(keyword in user_message.lower() for keyword in 
                        ["怎么", "如何", "为什么", "什么是", "计算", "原理", "方法", "规则", "解释"])
    
    think_content = []
    think_content.append("🤔 正在思考您的问题...")
    
    # 问题分析
    if needs_sql:
        think_content.append("\n📊 检测到数据查询需求:")
        think_content.append("- 用户想要查询具体的数据")
        think_content.append("- 需要调用SQL工具来获取数据")
        think_content.append("- 可能需要格式化展示结果")
    elif needs_knowledge:
        think_content.append("\n📚 检测到知识查询需求:")
        think_content.append("- 用户想要了解概念或原理")
        think_content.append("- 需要搜索知识库或基于已有知识回答")
        think_content.append("- 重点是解释清楚概念")
    else:
        think_content.append("\n💭 这是一个一般性问题:")
        think_content.append("- 用户可能是在对话交流")
        think_content.append("- 需要友好地回应")
    
    # 🆕 动态工具使用分析 - 基于实际执行历史
    if tool_used and tool_calls_history:
        think_content.append("\n🔧 工具调用分析:")
        
        # 统计各种工具的调用情况
        sql_calls = []
        knowledge_calls = []
        other_calls = []
        
        for i, tool_info in enumerate(tool_calls_history, 1):
            tool_name = tool_info["tool_call"]["tool_name"]
            success = tool_info["tool_result"]["success"]
            
            # 获取用时信息
            timing_info = tool_info.get("timing", {})
            tool_duration = timing_info.get("tool_duration", 0)
            
            if tool_name == "integrated_sql":
                sql_calls.append({
                    "step": i,
                    "success": success,
                    "duration": tool_duration,
                    "parameters": tool_info["tool_call"].get("parameters", {}),
                    "error": tool_info["tool_result"].get("error", "")
                })
            elif tool_name == "knowledge_search":
                knowledge_calls.append({
                    "step": i,
                    "success": success,
                    "duration": tool_duration,
                    "parameters": tool_info["tool_call"].get("parameters", {}),
                    "result": tool_info["tool_result"].get("result", {})
                })
            else:
                other_calls.append({
                    "step": i,
                    "tool_name": tool_name,
                    "success": success,
                    "duration": tool_duration
                })
        
        # 显示SQL工具调用情况
        if sql_calls:
            for sql_call in sql_calls:
                if sql_call["success"]:
                    think_content.append(f"- ✅ 步骤{sql_call['step']}: SQL工具成功执行，获得了数据 (用时: {sql_call['duration']:.2f}秒)")
                    think_content.append("  📊 从数据库获取了具体的数值和统计信息")
                else:
                    think_content.append(f"- ❌ 步骤{sql_call['step']}: SQL工具执行失败 (用时: {sql_call['duration']:.2f}秒)")
                    # 显示失败原因的简要说明
                    error_msg = sql_call["error"]
                    if "Unknown column" in error_msg:
                        think_content.append("  💡 检测到字段不存在问题，可能需要查询正确的表结构")
                    elif "syntax error" in error_msg.lower():
                        think_content.append("  💡 检测到SQL语法错误，需要优化查询语句")
                    else:
                        think_content.append(f"  ⚠️ 错误详情: {error_msg[:100]}...")
        
        # 显示知识库搜索情况
        if knowledge_calls:
            for knowledge_call in knowledge_calls:
                if knowledge_call["success"]:
                    result = knowledge_call["result"]
                    result_count = result.get("result_count", 0) if isinstance(result, dict) else 0
                    think_content.append(f"- ✅ 步骤{knowledge_call['step']}: 知识库搜索成功，找到了{result_count}条相关信息 (用时: {knowledge_call['duration']:.2f}秒)")
                    think_content.append("  📚 获取了业务逻辑和概念解释信息")
                else:
                    think_content.append(f"- ❌ 步骤{knowledge_call['step']}: 知识库搜索没有找到相关信息 (用时: {knowledge_call['duration']:.2f}秒)")
        
        # 显示其他工具调用情况
        if other_calls:
            for other_call in other_calls:
                status = "✅" if other_call["success"] else "❌"
                think_content.append(f"- {status} 步骤{other_call['step']}: {other_call['tool_name']}工具执行{'完成' if other_call['success'] else '失败'} (用时: {other_call['duration']:.2f}秒)")
        
        # 整合策略说明
        total_tools = len(tool_calls_history)
        successful_tools = sum(1 for tool_info in tool_calls_history if tool_info["tool_result"]["success"])
        
        if total_tools > 1:
            think_content.append(f"\n📝 多步骤执行: 共进行了{total_tools}步操作，其中{successful_tools}步成功")
            
            # 分析执行策略
            if sql_calls and knowledge_calls:
                think_content.append("- 🔄 策略: 先尝试数据查询，然后补充业务逻辑信息")
            elif len(sql_calls) > 1:
                think_content.append("- 🔄 策略: 多次SQL查询以获取完整数据")
            elif len(knowledge_calls) > 1:
                think_content.append("- 🔄 策略: 多次知识库搜索以获取全面信息")
        
    else:
        think_content.append("\n⚠️ 检测到Agent没有调用工具:")
        think_content.append("- 可能是数据查询问题但Agent选择了直接回答")
        think_content.append("- 建议明确指定需要查询数据库或知识库")
    
    # 回答策略
    think_content.append("\n📝 回答策略:")
    if tool_used:
        think_content.append("- 整合工具结果和分析")
        think_content.append("- 用清晰的格式展示信息")
        think_content.append("- 确保用户容易理解")
        
        # 基于实际工具执行情况给出策略
        if tool_calls_history:
            failed_calls = [tool_info for tool_info in tool_calls_history if not tool_info["tool_result"]["success"]]
            if failed_calls:
                think_content.append("- 🔧 处理执行失败的工具调用，提供替代方案或说明")
    else:
        think_content.append("- 提供准确、有用的信息")
        think_content.append("- 保持友好的对话语调")
    
    think_content.append("\n✨ 正在整理最终答案...")
    
    think_text = "\n".join(think_content)
    return f"<think>\n{think_text}\n</think>"


def check_service_connections():
    """检查关键服务连接"""
    print("🔍 检查关键服务连接...")

    # 1. 检查LLM服务
    try:
        print("  📡 检查LLM服务连接...")
        llm = LLMFactory.create_chat_model(provider="local", timeout=10)
        # 发送测试请求
        test_response = llm.invoke("测试连接")
        print("  ✅ LLM服务连接正常")
    except Exception as e:
        print(f"  ❌ LLM服务连接失败: {e}")
        raise Exception(f"LLM服务不可用: {e}")

    # 2. 检查数据库连接
    try:
        print("  🗄️ 检查数据库连接...")
        from src.config.database_config import DatabaseConfig
        db_config = DatabaseConfig.get_mysql_config()
        # 这里可以添加实际的数据库连接测试
        print("  ✅ 数据库配置正常")
    except Exception as e:
        print(f"  ❌ 数据库配置检查失败: {e}")
        # 数据库连接失败不阻止启动，因为可能有些查询不需要数据库
        print("  ⚠️ 数据库连接问题，但继续启动")

    # 3. 检查Vanna服务（如果启用）
    try:
        print("  🧠 检查Vanna服务连接...")
        # 这里可以添加Vanna服务的连接检查
        print("  ✅ Vanna服务配置正常")
    except Exception as e:
        print(f"  ⚠️ Vanna服务检查失败: {e}")
        # Vanna失败不阻止启动

    print("✅ 关键服务检查完成")


def init_sql_agent():
    """初始化SQL Agent"""
    global _agent_executor
    
    try:
        logger.info("🤖 初始化SQL Agent...")
        print("🤖 初始化SQL Agent...")
        
        # 🚀 优化: 使用较低的temperature提高响应速度
        llm = LLMFactory.create_chat_model(
            provider="local",
            temperature=PERFORMANCE_CONFIG["temperature"],  # 从0.1改为0.3，平衡速度和质量
            preserve_think_tags=False,  # 关闭think标签，提高响应速度
            timeout=PERFORMANCE_CONFIG["timeout"]
        )
        
        tools = get_all_tools()
        
        # 🚀 优化: 使用向量增强Agent，支持复杂提示词
        agent = VectorEnhancedAgent(
            llm=llm,
            tools=tools,
            max_iterations=PERFORMANCE_CONFIG["max_iterations"],  # 从配置读取最大迭代次数
            system_prompt=_get_optimized_system_prompt()  # 使用优化的系统提示词
        )
        
        _agent_executor = agent
        
        logger.info(f"✅ SQL Agent初始化成功，工具数量: {len(tools)}")
        print(f"✅ SQL Agent初始化成功，工具数量: {len(tools)}")
        
        # 记录可用工具
        tool_names = [tool.name for tool in tools]
        logger.info(f"📋 可用工具: {tool_names}")
        print(f"📋 可用工具: {tool_names}")
        
    except Exception as e:
        logger.error(f"❌ SQL Agent初始化失败: {e}")
        print(f"❌ SQL Agent初始化失败: {e}")
        raise e


def _get_optimized_system_prompt() -> str:
    """获取优化的系统提示词，简化内容提高响应速度"""
    return """你是一个高效的数据分析助手。可用工具：

1. **integrated_sql** - 查询数据库数据
   - 用于：电费、用电量、各种指标的具体数值查询
   - 参数：question (自然语言问题，如"2025年4月电费是多少")
   
2. **knowledge_search** - 搜索业务知识
   - 用于：指标计算方法、业务规则解释
   - 参数：query (查询问题，如"电费是怎么计算的")

🎯 **工具选择原则：**
- 用户问"多少"、"数据" → integrated_sql
- 用户问"什么"、"怎么计算" → knowledge_search

📋 **回答格式要求（必须严格遵守）：**
当使用integrated_sql工具成功获得数据后，必须按以下标准格式输出：

# 📊 [查询结果标题]

## 📈 数据概览
根据查询结果给出关键数据的简要说明。

## 📋 详细数据
**关键要求：必须完全按照integrated_sql工具返回的markdown表格原样输出，不得修改表格的列名、列顺序、数据格式或表格结构。但是需要将数据保留2位小数，禁止重新组织或美化表格内容。**

integrated_sql工具返回的表格格式如下：
```
**查询结果：**

| 原始列名1 | 原始列名2 | ... |
| --- | --- | --- |
| 数据1 | 数据2 | ... |
```

请将上述表格内容原样复制到此处，不要修改任何内容。

## 💡 数据说明
简要解释数据的含义和重要性，但不要重新制作表格。

⚡ **工具调用原则（重要）：**
- 根据用户需求和数据分析结果，可能需要调用多个工具
- 如果数据分析发现问题或需要改进建议，应调用knowledge_search工具
- 绝对不要使用"最终答案："、"计算逻辑为："等技术性表述
- 基于所有工具返回的数据给出完整的用户友好回答

🚫 **禁止行为：**
- 禁止重新命名表格列名（如将"prv_name"改为"项目"）
- 禁止重新组织表格结构（如将多列合并为少列）
- 禁止创建新的表格来替代工具输出的表格

**正确的工具调用格式：**
```json
{"tool_name": "integrated_sql", "parameters": {"question": "2025年4月电费是多少"}}
```

**重要：integrated_sql工具只接受自然语言问题，不接受SQL语句！工具会自动返回markdown格式的表格，必须原样使用其输出结果。**"""


def get_or_create_session_executor(session_id: str) -> EnhancedToolCallingAgent:
    """获取或创建会话专用的执行器"""
    if session_id not in _session_executors:
        # 🚀 优化: 为会话创建高性能配置的executor
        llm = LLMFactory.create_chat_model(
            provider="local", 
            temperature=PERFORMANCE_CONFIG["temperature"], 
            preserve_think_tags=False,  # 关闭think标签，提高响应速度
            timeout=PERFORMANCE_CONFIG["timeout"]
        )
        tools = get_all_tools()
        agent = VectorEnhancedAgent(
            llm=llm,
            tools=tools,
            max_iterations=PERFORMANCE_CONFIG["max_iterations"],  # 从配置读取最大迭代次数
            system_prompt=_get_optimized_system_prompt()
        )
        _session_executors[session_id] = agent
    
    return _session_executors[session_id]


# ===== API 端点 =====

@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "Analysis Agent API Server",
        "version": "1.0.0",
        "description": "基于AI的数据分析Agent服务，支持SQL查询和知识库搜索",
        "capabilities": [
            "自然语言转SQL查询",
            "数据库查询执行",
            "知识库搜索",
            "业务逻辑查询",
            "指标计算说明"
        ],
        "endpoints": {
            "/": "服务信息",
            "/health": "健康检查",
            "/stats": "性能统计",
            "/sql/query": "SQL查询接口",
            "/sql/chat": "对话式查询接口"
        },
        "performance_config": PERFORMANCE_CONFIG,
        "optimization_enabled": True
    }


@app.get("/stats")
async def get_performance_stats():
    """获取性能统计信息"""
    stats = performance_monitor.get_stats()
    return {
        "performance_stats": stats,
        "server_uptime": time.time() - app.state.start_time if hasattr(app.state, 'start_time') else 0,
        "config": PERFORMANCE_CONFIG
    }


@app.get("/v1/models")
async def list_models():
    """列出可用模型 - Dify兼容性必需"""
    models = []
    
    # 添加Analysis Agent模型
    models.append({
        "id": "analysis-agent",
        "object": "model", 
        "created": get_current_timestamp(),
        "owned_by": "local"
    })
    
    # 如果有配置的模型名称，也添加它
    if hasattr(settings, 'local_llm_model_name') and settings.local_llm_model_name:
        models.append({
            "id": settings.local_llm_model_name,
            "object": "model",
            "created": get_current_timestamp(), 
            "owned_by": "local"
        })
    
    return {
        "object": "list",
        "data": models
    }


@app.post("/v1/sql/query", response_model=SqlQueryResponse)
async def sql_query(request: SqlQueryRequest):
    """SQL查询端点 - 专门用于SQL相关查询"""
    start_time = time.time()
    
    try:
        # 生成会话ID
        session_id = request.session_id or f"session-{uuid.uuid4().hex[:8]}"
        
        # 获取会话执行器
        executor = get_or_create_session_executor(session_id)
        
        # 执行查询
        result = executor.process_message(request.question)
        
        execution_time_ms = (time.time() - start_time) * 1000
        
        # 🔍 记录性能数据
        tool_calls = len(result.get("tool_calls_history", []))
        llm_calls = result.get("iterations", 1)
        llm_time = sum(call.get("timing", {}).get("llm_duration", 0) for call in result.get("tool_calls_history", []))
        
        performance_monitor.record_request(
            duration=execution_time_ms / 1000,
            tool_calls=tool_calls,
            llm_calls=llm_calls,
            llm_time=llm_time
        )
        
        logger.info(f"📊 [性能] 查询完成: {execution_time_ms:.0f}ms, 工具调用: {tool_calls}次, LLM调用: {llm_calls}次")
        
        # 解析结果
        if result.get("tool_used") and "tool_calls_history" in result:
            # 查找集成SQL工具的调用结果
            sql_result = None
            for tool_info in result["tool_calls_history"]:
                if tool_info["tool_call"]["tool_name"] == "integrated_sql":
                    sql_result = tool_info["tool_result"]
                    break
            
            if sql_result and sql_result.get("success"):
                tool_result = sql_result["result"]
                # 检查Agent的原始响应是否已经包含了美化的格式
                original_response = result["response"]
                
                # 如果Agent响应包含表格格式，直接使用
                if "| " in original_response and "|-" in original_response:
                    formatted_response = original_response
                else:
                    # 否则使用我们的格式化函数
                    formatted_response = original_response
                
                # 🆕 添加下一步建议到SQL查询响应
                # 检查是否已包含下一步建议，避免重复添加
                if "下一步你可能会问" not in formatted_response:
                    next_step_suggestion = generate_next_step_suggestion(
                        user_message=request.question,
                        agent_response=formatted_response,
                        tool_used=True,
                        tool_calls_history=result.get("tool_calls_history", [])
                    )
                    if next_step_suggestion.strip():  # 只有当建议不为空时才添加
                        formatted_response = formatted_response + next_step_suggestion
                
                return SqlQueryResponse(
                    success=True,
                    answer=formatted_response,
                    generated_sql=tool_result.get("generated_sql") if request.include_sql else None,
                    data=tool_result.get("execution_result", {}).get("data") if request.include_data else None,
                    row_count=tool_result.get("execution_result", {}).get("row_count"),
                    execution_time_ms=execution_time_ms,
                    session_id=session_id
                )
            else:
                # 工具调用失败
                error_msg = sql_result.get("error") if sql_result else "未找到SQL工具调用结果"
                failed_response = result["response"]
                
                # 🆕 为失败的响应也添加下一步建议
                if "下一步你可能会问" not in failed_response:
                    next_step_suggestion = generate_next_step_suggestion(
                        user_message=request.question,
                        agent_response=failed_response,
                        tool_used=False,
                        tool_calls_history=result.get("tool_calls_history", [])
                    )
                    if next_step_suggestion.strip():  # 只有当建议不为空时才添加
                        failed_response = failed_response + next_step_suggestion
                
                return SqlQueryResponse(
                    success=False,
                    answer=failed_response,
                    error=error_msg,
                    execution_time_ms=execution_time_ms,
                    session_id=session_id
                )
        else:
            # 没有使用工具，可能是一般性对话
            general_response = result["response"]
            
            # 🆕 为一般性对话也添加下一步建议
            if "下一步你可能会问" not in general_response:
                next_step_suggestion = generate_next_step_suggestion(
                    user_message=request.question,
                    agent_response=general_response,
                    tool_used=False,
                    tool_calls_history=[]
                )
                if next_step_suggestion.strip():  # 只有当建议不为空时才添加
                    general_response = general_response + next_step_suggestion
            
            return SqlQueryResponse(
                success=True,
                answer=general_response,
                execution_time_ms=execution_time_ms,
                session_id=session_id
            )
            
    except Exception as e:
        execution_time_ms = (time.time() - start_time) * 1000
        return SqlQueryResponse(
            success=False,
            answer=f"查询处理失败: {str(e)}",
            error=str(e),
            execution_time_ms=execution_time_ms,
            session_id=request.session_id or "unknown"
        )


@app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
async def chat_completions(request: ChatCompletionRequest):
    """兼容OpenAI的聊天完成端点 - 供Dify等平台使用"""
    logger.info(f"🚀 [API调用] /v1/chat/completions 端点被调用")
    print(f"🚀 DEBUG: [sql_agent_server] /v1/chat/completions 端点被调用")
    print(f"  - 请求体: {request.model_dump()}")
    print(f"  - 是否流式: {request.stream}")
    
    try:
        # 🆕 正确提取对话历史和当前消息
        conversation_history = []
        system_message = ""
        current_user_message = ""
        
        for message in request.messages:
            if message.role == "system":
                system_message += message.content + "\n"
            elif message.role == "user":
                # 除了最后一个user消息，其他都加入历史
                if current_user_message:  # 如果已有user消息，说明这是历史消息
                    conversation_history.append({
                        "role": "user",
                        "content": current_user_message
                    })
                current_user_message = message.content  # 更新为当前消息
            elif message.role == "assistant":
                conversation_history.append({
                    "role": "assistant", 
                    "content": message.content
                })
        
        if not current_user_message:
            raise HTTPException(status_code=400, detail="未找到用户消息")
        
        # 🔍 记录用户输入和对话历史
        logger.info(f"👤 [用户输入] '{current_user_message}'")
        logger.info(f"📚 [对话历史] {len(conversation_history)} 条历史记录")
        if conversation_history:
            for i, hist in enumerate(conversation_history[-3:], 1):  # 只记录最近3条
                logger.info(f"  [{i}] {hist['role']}: {hist['content'][:100]}...")
        
        # 使用全局执行器处理
        if not _agent_executor:
            raise HTTPException(status_code=500, detail="SQL Agent未初始化")
        
        print(f"  - 系统消息: '{system_message.strip()}'")
        print(f"  - 对话历史: {len(conversation_history)} 条记录")
        print(f"  - 当前用户消息: '{current_user_message}'")
        
        # 🆕 构建完整的用户消息（包含系统提示）
        if system_message.strip():
            final_user_message = f"{system_message.strip()}\n\n用户问题: {current_user_message}"
        else:
            final_user_message = current_user_message
        
        logger.info(f"🔧 [最终消息] Agent将处理: '{final_user_message}'")
        print(f"  - 最终消息: '{final_user_message}'")
        
        # 检查是否需要流式响应
        if request.stream:
            print("  - 返回实时进度流式响应")
            return StreamingResponse(
                stream_sql_agent_with_real_progress(request, final_user_message, conversation_history, system_message.strip() if system_message.strip() else None),
                media_type="text/plain"
            )
        else:
            print("  - 返回非流式响应")
            
            # 根据请求参数决定是否保留think标签 - 强化默认值处理
            show_think = getattr(request, 'show_think_tags', True)
            if show_think is None:
                show_think = True
            print(f"  - show_think_tags设置: {show_think}")
            
            # 如果需要控制think标签，临时创建一个LLM实例
            if not show_think:
                # 创建不保留think标签的LLM
                temp_llm = LLMFactory.create_chat_model(
                    provider="local", 
                    temperature=0.1, 
                    preserve_think_tags=False  # 关闭think标签，提高响应速度
                )
                tools = get_all_tools()
                temp_executor = EnhancedToolCallingAgent(llm=temp_llm, tools=tools, max_iterations=10)
                # 🚨 修复：传递对话历史和系统消息
                result = temp_executor.process_message(final_user_message, conversation_history, system_message.strip() if system_message.strip() else None)
            else:
                # 使用全局执行器（默认保留think标签）
                # 🚨 修复：传递对话历史和系统消息
                result = _agent_executor.process_message(final_user_message, conversation_history, system_message.strip() if system_message.strip() else None)
            
            # 检查是否使用了工具
            tool_used = result.get("tool_used", False)
            tool_calls_history = result.get("tool_calls_history", [])
            print(f"  - 是否使用工具: {tool_used}")
            
            # 显示用时统计
            timing_info = result.get("timing", {})
            if timing_info:
                total_duration = timing_info.get("total", 0)
                print(f"  - 总处理用时: {total_duration:.2f}秒")
                
                # LLM调用统计
                llm_calls = timing_info.get("llm_calls", [])
                if llm_calls:
                    total_llm_time = sum(call.get("duration", 0) for call in llm_calls)
                    print(f"  - LLM处理: {total_llm_time:.2f}秒 ({len(llm_calls)}次调用)")
                
                # 工具调用统计
                tool_timing_calls = timing_info.get("tool_calls", [])
                if tool_timing_calls:
                    total_tool_time = sum(call.get("duration", 0) for call in tool_timing_calls)
                    print(f"  - 工具执行: {total_tool_time:.2f}秒 ({len(tool_timing_calls)}个工具)")
            
            if tool_used and "tool_calls_history" in result:
                print(f"  - 工具调用历史: {len(result['tool_calls_history'])} 次调用")
                for i, tool_info in enumerate(result["tool_calls_history"]):
                    tool_name = tool_info['tool_call']['tool_name']
                    success = tool_info['tool_result']['success']
                    
                    # 显示工具用时信息
                    tool_timing = tool_info.get('timing', {})
                    tool_duration = tool_timing.get('tool_duration', 0)
                    
                    print(f"    [{i+1}] 工具: {tool_name} (用时: {tool_duration:.2f}秒)")
                    print(f"    [{i+1}] 成功: {success}")
            
            # 确保content不为空，使用Agent的原始响应
            content = result.get("response", "抱歉，我无法处理您的请求。")
            if not content or content.strip() == "":
                content = "查询完成，但未获取到有效响应。"
            
            # 如果没有使用工具但是问题明显需要查询数据库，给出提示
            if not tool_used and any(keyword in current_user_message for keyword in ["电费", "用水", "用电", "查询", "多少", "数据", "统计", "报表", "怎么", "如何", "什么是"]):
                content = f"检测到您的问题可能需要查询数据库或知识库，但Agent没有调用相应工具。原始回答：{content}\n\n💡 建议：请尝试更明确地表达您的查询需求，例如：\n- 数据查询：\"请查询2025年1月的电费数据\"\n- 知识查询：\"请解释电费是如何计算的\""
            
            # 🆕 如果需要显示think标签，手动添加（但不包含下一步建议）
            if show_think:
                think_tags_only = generate_think_tags_only(
                    user_message=current_user_message,
                    tool_used=tool_used,
                    tool_calls_history=tool_calls_history
                )
                content = think_tags_only + "\n\n" + content
                print(f"  - 添加了手动生成的think标签")
            
            # 🆕 最后添加下一步建议到响应末尾（无论是否有think标签）
            # 检查Agent响应是否已经包含下一步建议，避免重复添加
            original_agent_response = result.get("response", "")
            if "下一步你可能会问" not in original_agent_response:
                next_step_suggestion = generate_next_step_suggestion(
                    user_message=current_user_message,
                    agent_response=original_agent_response,  # 使用原始agent响应
                    tool_used=tool_used,
                    tool_calls_history=tool_calls_history
                )
                if next_step_suggestion.strip():  # 只有当建议不为空时才添加
                    content = content + next_step_suggestion
                    print(f"  - 已添加下一步建议")
                else:
                    print(f"  - Agent响应已包含建议，跳过重复添加")
            
            print(f"  - Agent响应: '{content[:100]}...' (显示前100字符)")
            
            # 非流式响应
            completion_id = generate_completion_id()
            timestamp = get_current_timestamp()
            
            # 计算token使用量
            prompt_tokens = sum(estimate_tokens(msg.content) for msg in request.messages)
            completion_tokens = estimate_tokens(content)
            
            # 使用Pydantic模型构造响应，与openai_compatible_server.py保持一致
            return ChatCompletionResponse(
                id=completion_id,
                created=timestamp,
                model=request.model,
                choices=[
                    ChatCompletionChoice(
                        index=0,
                        message=ChatMessage(role="assistant", content=content),
                        finish_reason="stop"
                    )
                ],
                usage=ChatCompletionUsage(
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=prompt_tokens + completion_tokens
                )
            )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 其他异常转换为HTTP异常
        print(f"❌ 处理请求时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"处理请求时发生错误: {str(e)}")


async def stream_sql_agent_with_real_progress(request: ChatCompletionRequest, user_message: str, conversation_history: List[Dict[str, str]], system_message: str = None):
    """使用Agent真实回调的流式响应生成器"""
    import time
    import asyncio
    
    completion_id = generate_completion_id()
    created = get_current_timestamp()
    
    # 🔍 记录流式请求的详细信息
    logger.info(f"🌊 [流式请求] 开始处理流式响应")
    logger.info(f"👤 [流式用户输入] '{user_message}'")
    logger.info(f"📚 [流式对话历史] {len(conversation_history)} 条历史记录")
    
    # 获取是否显示think标签的设置 - 强化默认值处理
    show_think = getattr(request, 'show_think_tags', True)
    if show_think is None:
        show_think = True
    
    logger.info(f"🎭 [流式设置] show_think_tags={show_think}")
    print(f"🌊 DEBUG: 开始真正实时流式输出，show_think_tags={show_think}")
    
    # 🆕 智能分段输出函数：表格瞬间输出，其他内容流式输出
    async def smart_stream_with_instant_tables(content: str, has_sql_result: bool, send_chunk_func, send_text_streaming_func, send_single_chunk_func, tool_calls_history):
        """智能处理表格和文本的流式输出"""
        import re
        import time
        
        if not has_sql_result:
            # 没有SQL结果，全部流式输出
            async for chunk in send_text_streaming_func(content, 0.03):
                yield chunk
            return
        
        # 🔧 修复：当有SQL结果时，确保表格数据能够显示
        # 首先检测Agent响应中是否已包含markdown表格
        table_pattern = r'(\|[^\n]*\|\s*\n\|[-\s|]*\|\s*\n(?:\|[^\n]*\|\s*\n)*)'
        tables = list(re.finditer(table_pattern, content, re.MULTILINE))
        
        if tables:
            # Agent响应中包含表格，按原逻辑处理
            print(f"🔍 DEBUG: 检测到{len(tables)}个表格，将瞬间输出")
            
            last_end = 0
            for i, table_match in enumerate(tables):
                table_start = table_match.start()
                table_end = table_match.end()
                table_content = table_match.group(1)
                
                # 输出表格前的文本（流式）
                before_table = content[last_end:table_start]
                if before_table.strip():
                    async for chunk in send_text_streaming_func(before_table, 0.03):
                        yield chunk
                
                # 🚀 表格瞬间输出
                print(f"📊 DEBUG: 瞬间输出第{i+1}个表格 ({len(table_content)}字符)")
                async for chunk in send_single_chunk_func(table_content, 0.1):
                    yield chunk
                
                last_end = table_end
            
            # 输出表格后的剩余文本（流式）
            remaining_text = content[last_end:]
            if remaining_text.strip():
                async for chunk in send_text_streaming_func(remaining_text, 0.03):
                    yield chunk
        else:
            # 🔧 修复关键逻辑：Agent响应中没有表格，从SQL工具的final_result中提取表格
            print(f"🔧 DEBUG: Agent响应中未检测到表格格式，从SQL工具结果中提取表格数据")
            
            # 查找SQL工具的执行结果 - 正确的数据位置
            sql_table_content = None
            if tool_calls_history:
                for tool_info in tool_calls_history:
                    if (tool_info["tool_call"]["tool_name"] == "integrated_sql" and 
                        tool_info["tool_result"]["success"]):
                        # 🚀 修复：从正确的位置提取表格内容
                        tool_result = tool_info["tool_result"]["result"]
                        if isinstance(tool_result, dict):
                            # final_result是一个字典，包含summary字段，summary包含完整的格式化表格
                            summary_content = tool_result.get("summary", "")
                            if isinstance(summary_content, str) and "| " in summary_content and "|-" in summary_content:
                                # 从summary中提取表格部分
                                tool_tables = list(re.finditer(table_pattern, summary_content, re.MULTILINE))
                                if tool_tables:
                                    sql_table_content = tool_tables[0].group(1)
                                    print(f"📊 DEBUG: 从SQL工具summary中提取到表格 ({len(sql_table_content)}字符)")
                                    break
                        
                        # 🔧 备用方案：如果summary中没有，尝试从execution_result构建表格
                        if not sql_table_content:
                            execution_result = tool_result.get("execution_result", {})
                            columns = execution_result.get("columns", [])
                            data = execution_result.get("data", [])
                            
                            if columns and data:
                                # 手动构建表格
                                table_lines = []
                                table_lines.append("**查询结果：**")
                                table_lines.append("")
                                
                                # 表头
                                header_line = "| " + " | ".join(columns) + " |"
                                separator_line = "| " + " | ".join(["---"] * len(columns)) + " |"
                                table_lines.append(header_line)
                                table_lines.append(separator_line)
                                
                                # 数据行（限制显示前10行）
                                for row in data[:10]:
                                    row_parts = []
                                    for col in columns:
                                        value = row.get(col, '')
                                        # 简单格式化
                                        if value is None:
                                            formatted_value = "null"
                                        elif isinstance(value, float):
                                            formatted_value = f"{value:.2f}"
                                        else:
                                            formatted_value = str(value)
                                        
                                        # 截断过长的值
                                        if len(formatted_value) > 30:
                                            formatted_value = formatted_value[:27] + "..."
                                        row_parts.append(formatted_value)
                                    
                                    row_line = "| " + " | ".join(row_parts) + " |"
                                    table_lines.append(row_line)
                                
                                sql_table_content = "\n".join(table_lines[2:])  # 不包括标题和空行
                                print(f"📊 DEBUG: 从execution_result手动构建表格 ({len(sql_table_content)}字符)")
                        break
            
            # 先流式输出Agent的文本响应
            if content.strip():
                async for chunk in send_text_streaming_func(content, 0.03):
                    yield chunk
                
                # 如果Agent响应后没有换行，添加一个
                if not content.endswith('\n'):
                    async for chunk in send_text_streaming_func("\n\n", 0.03):
                        yield chunk
            
            # 如果找到了SQL表格数据，瞬间输出
            if sql_table_content:
                print(f"📊 DEBUG: 补充输出SQL工具的表格数据")
                async for chunk in send_single_chunk_func("**查询结果：**\n\n", 0.1):
                    yield chunk
                async for chunk in send_single_chunk_func(sql_table_content, 0.1):
                    yield chunk
            else:
                print(f"⚠️ DEBUG: 未能从SQL工具结果中提取表格数据")
                # 🔧 进一步调试：输出tool_calls_history的结构
                if tool_calls_history:
                    for i, tool_info in enumerate(tool_calls_history):
                        if tool_info["tool_call"]["tool_name"] == "integrated_sql":
                            result_keys = list(tool_info["tool_result"]["result"].keys()) if isinstance(tool_info["tool_result"]["result"], dict) else "非字典类型"
                            print(f"🔍 DEBUG: SQL工具{i+1}结果结构: {result_keys}")
    
    # 🆕 真实Agent回调显示器
    class RealAgentThinkDisplay:
        def __init__(self, send_chunk_func, send_text_streaming_func):
            self.send_chunk = send_chunk_func
            self.send_text_streaming = send_text_streaming_func
            self.think_started = False
            self.current_iteration = 0
            self.think_queue = asyncio.Queue()
        
        async def start_think(self):
            """开始think标签"""
            if not self.think_started:
                async for chunk in self.send_chunk("<think>\n"):
                    yield chunk
                async for chunk in self.send_text_streaming("🤔 正在分析您的问题...", 0.02):
                    yield chunk
                self.think_started = True
                await asyncio.sleep(0.2)
        
        async def agent_callback(self, event_type: str, data: dict):
            """Agent实时回调处理器"""
            try:
                if event_type == "iteration_start":
                    iteration = data.get("iteration", 0)
                    self.current_iteration = iteration
                    await self.think_queue.put(f"\n\n🔄 开始第 {iteration} 次思考...")
                
                elif event_type == "llm_start":
                    iteration = data.get("iteration", 0)
                    input_count = data.get("input_messages", 0)
                    await self.think_queue.put(f"\n🧠 正在调用AI模型... (输入消息: {input_count}条)")
                
                elif event_type == "llm_complete":
                    iteration = data.get("iteration", 0)
                    duration = data.get("duration", 0)
                    output_length = data.get("output_length", 0)
                    await self.think_queue.put(f"\n✅ AI模型响应完成 (用时: {duration:.2f}秒, 输出: {output_length}字符)")
                
                elif event_type == "tool_start":
                    iteration = data.get("iteration", 0)
                    tool_name = data.get("tool_name", "未知工具")
                    parameters = data.get("parameters", {})
                    await self.think_queue.put(f"\n🔧 开始调用工具: {tool_name}")
                    if parameters:
                        # 显示关键参数
                        key_params = []
                        for key, value in parameters.items():
                            if len(str(value)) > 50:
                                key_params.append(f"{key}='{str(value)[:47]}...'")
                            else:
                                key_params.append(f"{key}='{value}'")
                        if key_params:
                            await self.think_queue.put(f"\n   参数: {', '.join(key_params[:3])}")  # 只显示前3个参数
                
                elif event_type == "tool_complete":
                    iteration = data.get("iteration", 0)
                    tool_name = data.get("tool_name", "未知工具")
                    success = data.get("success", False)
                    duration = data.get("duration", 0)
                    
                    status = "✅ 成功" if success else "❌ 失败"
                    await self.think_queue.put(f"\n{status} {tool_name}工具执行完成 (用时: {duration:.2f}秒)")
                    
                    if success and tool_name == "integrated_sql":
                        result = data.get("result", {})
                        if isinstance(result, dict):
                            # 显示数据摘要
                            exec_result = result.get("execution_result", {})
                            row_count = exec_result.get("row_count", 0)
                            if row_count > 0:
                                await self.think_queue.put(f"\n📊 获取到 {row_count} 行数据")
                
                elif event_type == "iteration_complete":
                    iteration = data.get("iteration", 0)
                    duration = data.get("duration", 0)
                    tool_used = data.get("tool_used", False)
                    
                    if tool_used:
                        await self.think_queue.put(f"\n⏱️ 第 {iteration} 次思考完成 (用时: {duration:.2f}秒)")
                    else:
                        await self.think_queue.put(f"\n🎯 思考完成，准备输出答案...")
                
            except Exception as e:
                logger.warning(f"⚠️ [回调] 处理回调事件失败 ({event_type}): {e}")
        
        async def end_think(self):
            """结束think标签"""
            if self.think_started:
                async for chunk in self.send_text_streaming("\n\n✨ 分析完成，正在输出结果...", 0.02):
                    yield chunk
                async for chunk in self.send_chunk("\n</think>\n\n"):
                    yield chunk
                await asyncio.sleep(0.2)
    
    try:
        # 辅助函数：发送流式chunk
        async def send_chunk(content: str, finish_reason=None):
            chunk_data = {
                "id": completion_id,
                "object": "chat.completion.chunk",
                "created": created,
                "model": request.model,
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": content},
                        "finish_reason": finish_reason
                    }
                ]
            }
            chunk_json = json.dumps(chunk_data, ensure_ascii=False)
            yield f"data: {chunk_json}\n\n"
        
        # 辅助函数：逐字符流式发送文本
        async def send_text_streaming(text: str, char_delay: float = 0.03):
            """逐字符流式发送文本"""
            for char in text:
                async for chunk in send_chunk(char):
                    yield chunk
                await asyncio.sleep(char_delay)
        
        # 辅助函数：发送单个chunk并等待
        async def send_single_chunk(content: str, delay: float = 0.1):
            async for chunk in send_chunk(content):
                yield chunk
            await asyncio.sleep(delay)
        
        # 第一个chunk包含role
        first_chunk_data = {
            "id": completion_id,
            "object": "chat.completion.chunk",
            "created": created,
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "delta": {"role": "assistant", "content": ""},
                    "finish_reason": None
                }
            ]
        }
        chunk_json = json.dumps(first_chunk_data, ensure_ascii=False)
        yield f"data: {chunk_json}\n\n"
        await asyncio.sleep(0.1)
        
        # 🆕 创建真实Agent回调显示器
        think_display = RealAgentThinkDisplay(send_chunk, send_text_streaming)
        
        # 🆕 如果需要显示think标签，立即开始
        if show_think:
            async for chunk in think_display.start_think():
                yield chunk

        # 🔧 关键修复：使用Agent的异步回调机制
        start_time = time.time()
        
        # 根据show_think参数决定使用哪个executor
        if not show_think:
            # 创建保留think标签的临时executor
            temp_llm = LLMFactory.create_chat_model(
                provider="local",
                temperature=PERFORMANCE_CONFIG["temperature"],
                preserve_think_tags=False,  # 关闭think标签，提高响应速度
                timeout=PERFORMANCE_CONFIG["timeout"]
            )
            tools = get_all_tools()
            temp_executor = VectorEnhancedAgent(
                llm=temp_llm,
                tools=tools,
                max_iterations=PERFORMANCE_CONFIG["max_iterations"],  # 从配置读取最大迭代次数
                system_prompt=_get_optimized_system_prompt()
            )

            # 直接调用（不显示think标签）
            result = temp_executor.process_message(user_message, conversation_history, system_message)
        else:
            # 🚀 使用真实的异步回调机制
            # 创建异步队列来处理实时更新
            stream_queue = asyncio.Queue()
            
            # 实时回调函数：将更新放入队列
            async def real_time_callback(event_type: str, data: dict):
                # 先让think_display处理事件
                await think_display.agent_callback(event_type, data)
                # 然后从think_queue中获取更新并放入stream_queue
                while not think_display.think_queue.empty():
                    try:
                        update_text = think_display.think_queue.get_nowait()
                        await stream_queue.put(update_text)
                    except asyncio.QueueEmpty:
                        break
            
            # 并行执行Agent和实时显示
            async def run_agent():
                return await _agent_executor.process_message_async(
                    user_message,
                    conversation_history,
                    real_time_callback,
                    system_message
                )
            
            # 创建Agent任务
            agent_task = asyncio.create_task(run_agent())
            
            # 并行处理：实时显示think更新
            collected_events = []
            while not agent_task.done():
                try:
                    # 非阻塞检查队列
                    update_text = await asyncio.wait_for(stream_queue.get(), timeout=0.1)
                    collected_events.append(update_text)
                    # 实时发送更新
                    async for chunk in send_text_streaming(update_text, 0.02):
                        yield chunk
                except asyncio.TimeoutError:
                    # 没有新事件，继续等待
                    continue
                except Exception as e:
                    logger.warning(f"⚠️ [实时显示] 处理事件失败: {e}")
                    break
            
            # Agent完成，获取结果
            result = await agent_task
            
            # 处理队列中剩余的事件
            while not stream_queue.empty():
                try:
                    update_text = stream_queue.get_nowait()
                    collected_events.append(update_text)
                    async for chunk in send_text_streaming(update_text, 0.02):
                        yield chunk
                except asyncio.QueueEmpty:
                    break

        # 🔧 简化的执行完成显示
        if show_think:
            total_time = time.time() - start_time
            tool_used = result.get("tool_used", False)
            tool_calls_history = result.get("tool_calls_history", [])
            
            # 显示最终统计
            if tool_used and tool_calls_history:
                successful_tools = sum(1 for tool_info in tool_calls_history if tool_info["tool_result"]["success"])
                total_tools = len(tool_calls_history)
                async for chunk in send_text_streaming(f"\n\n📊 执行总结: {successful_tools}/{total_tools} 工具成功", 0.02):
                    yield chunk
            
            # 性能摘要
            async for chunk in send_text_streaming(f"\n⏱️ 总用时: {total_time:.1f}秒", 0.02):
                yield chunk
            
            # 结束think标签
            async for chunk in think_display.end_think():
                yield chunk

        # 输出最终结果 - 智能处理SQL表格和普通文本
        final_result = result.get("response", "")
        if final_result:
            # 🚀 新功能：检测是否包含SQL查询结果表格
            tool_used = result.get("tool_used", False)
            tool_calls_history = result.get("tool_calls_history", [])
            has_sql_result = False
            
            # 检查是否使用了SQL工具且成功
            if tool_used and tool_calls_history:
                for tool_info in tool_calls_history:
                    if (tool_info["tool_call"]["tool_name"] == "integrated_sql" and 
                        tool_info["tool_result"]["success"]):
                        has_sql_result = True
                        break
            
            # 智能分段输出：表格瞬间输出，其他内容流式输出
            async for chunk in smart_stream_with_instant_tables(final_result, has_sql_result, send_chunk, send_text_streaming, send_single_chunk, tool_calls_history):
                yield chunk
            
            # 🆕 添加下一步建议到流式响应
            # 检查Agent响应是否已经包含下一步建议，避免重复添加
            if "下一步你可能会问" not in final_result:
                next_step_suggestion = generate_next_step_suggestion(
                    user_message=user_message,
                    agent_response=final_result,
                    tool_used=tool_used,
                    tool_calls_history=tool_calls_history
                )
                if next_step_suggestion.strip():  # 只有当建议不为空时才添加
                    async for chunk in send_text_streaming(next_step_suggestion, 0.03):
                        yield chunk
                else:
                    print(f"🔍 DEBUG: Agent响应已包含建议，跳过重复添加")
        
        else:
            async for chunk in send_text_streaming("❓ 查询完成，但未获取到结果。", 0.04):
                yield chunk

        # 发送结束标记
        final_chunk_data = {
            "id": completion_id,
            "object": "chat.completion.chunk",
            "created": created,
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }
            ]
        }
        chunk_json = json.dumps(final_chunk_data, ensure_ascii=False)
        yield f"data: {chunk_json}\n\n"
        yield "data: [DONE]\n\n"
        
        print(f"🌊 DEBUG: 真实Agent回调流式输出完成")
        
    except Exception as e:
        print(f"❌ 流式输出错误: {e}")
        # 发送错误信息
        async for chunk in send_text_streaming(f"❌ **处理出错**: {str(e)}", 0.04):
            yield chunk
        
        # 发送结束标记
        final_chunk_data = {
            "id": completion_id,
            "object": "chat.completion.chunk",
            "created": created,
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }
            ]
        }
        chunk_json = json.dumps(final_chunk_data, ensure_ascii=False)
        yield f"data: {chunk_json}\n\n"
        yield "data: [DONE]\n\n"


@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查工具是否可用
        tools = get_all_tools()
        tool_count = len(tools)
        
        # 简单的数据库连接测试（可选）
        db_status = "ok"  # 这里可以添加实际的数据库连接检查
        
        return {
            "status": "healthy",
            "service": "analysis-agent",
            "timestamp": get_current_timestamp(),
            "tools_available": tool_count,
            "database_status": db_status
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "service": "analysis-agent",
            "timestamp": get_current_timestamp(),
            "error": str(e)
        }


@app.get("/v1/sessions")
async def list_sessions():
    """列出当前活跃的会话"""
    return {
        "active_sessions": list(_session_executors.keys()),
        "session_count": len(_session_executors)
    }


@app.delete("/v1/sessions/{session_id}")
async def clear_session(session_id: str):
    """清空指定会话的历史"""
    if session_id in _session_executors:
        # 重新创建一个新的agent实例来清空历史
        llm = LLMFactory.create_chat_model(provider="local", temperature=0.1, preserve_think_tags=False)
        tools = get_all_tools()
        _session_executors[session_id] = EnhancedToolCallingAgent(llm=llm, tools=tools, max_iterations=10)
        return {"message": f"会话 {session_id} 历史已清空"}
    else:
        raise HTTPException(status_code=404, detail="会话不存在")


@app.delete("/v1/sessions")
async def clear_all_sessions():
    """清空所有会话"""
    _session_executors.clear()
    return {"message": "所有会话已清空"}


class InteractiveAgentChat:
    """交互式Agent对话类"""
    
    def __init__(self):
        """初始化交互式对话"""
        self.agent = None
        self.session_id = f"interactive-{uuid.uuid4().hex[:8]}"
        self.conversation_history = []
        
    def initialize_agent(self):
        """初始化Agent"""
        try:
            print("🚀 正在初始化Analysis Agent...")
            
            # 创建LLM
            llm = LLMFactory.create_chat_model(
                provider="local",
                temperature=0.1,
                preserve_think_tags=False  # 关闭think标签，提高响应速度
            )
            
            # 创建Agent
            tools = get_all_tools()
            self.agent = EnhancedToolCallingAgent(
                llm=llm,
                tools=tools,
                max_iterations=10
            )
            
            print("✅ Analysis Agent初始化成功")
            print(f"📋 可用工具: {[tool.name for tool in self.agent.tools]}")
            return True
            
        except Exception as e:
            print(f"❌ Agent初始化失败: {e}")
            return False
    
    def print_streaming(self, text: str, delay: float = 0.02):
        """流式打印文本"""
        import sys
        import time
        
        for char in text:
            sys.stdout.write(char)
            sys.stdout.flush()
            time.sleep(delay)
        print()  # 换行
    
    def process_message_with_streaming(self, user_input: str):
        """带流式输出的消息处理"""
        import sys
        import time
        
        # 🔍 记录用户输入
        logger.info(f"👤 [用户输入] '{user_input}'")
        
        # 第一阶段：开始think标签
        self.print_streaming("<think>", 0.02)
        self.print_streaming("🤔 正在思考您的问题...", 0.02)
        time.sleep(0.2)
        
        # 问题类型分析
        if any(keyword in user_input.lower() for keyword in ["电费", "用水", "用电", "查询", "多少", "数据", "统计", "报表", "年", "月", "日", "度", "费用"]):
            self.print_streaming("\n\n📊 检测到数据查询需求，正在处理...", 0.02)
        else:
            self.print_streaming("\n\n💭 正在分析和处理...", 0.02)
        
        self.print_streaming("\n\n🧠 正在调用AI模型...", 0.02)
        time.sleep(0.2)

        try:
            start_time = time.time()
            # 直接执行Agent，无额外等待
            result = self.agent.process_message(user_input, self.conversation_history)
            processing_time = time.time() - start_time
            
            # 🆕 简化的结果显示
            tool_used = result.get("tool_used", False)
            tool_calls_history = result.get("tool_calls_history", [])
            
            if tool_used and tool_calls_history:
                self.print_streaming("\n\n🔧 工具执行完成:", 0.02)
                
                # 快速摘要显示
                successful_tools = sum(1 for tool_info in tool_calls_history if tool_info["tool_result"]["success"])
                total_tools = len(tool_calls_history)
                self.print_streaming(f"\n✅ 共执行{total_tools}个工具，{successful_tools}个成功", 0.02)
                
                # 显示主要工具结果
                for tool_info in tool_calls_history:
                    tool_name = tool_info["tool_call"]["tool_name"]
                    success = tool_info["tool_result"]["success"]
                    
                    if tool_name == "integrated_sql" and success:
                        self.print_streaming(f"\n📊 SQL查询成功，已获取数据表格", 0.02)
                    elif tool_name == "knowledge_search" and success:
                        self.print_streaming(f"\n📚 知识搜索成功，找到相关信息", 0.02)
                
            else:
                self.print_streaming("\n\n⚠️ Agent没有调用工具，可能需要明确指定查询需求", 0.02)
            
            # 结束think标签
            self.print_streaming("\n\n✨ 整理最终答案...", 0.02)
            self.print_streaming("</think>\n", 0.02)
            time.sleep(0.2)
            
            # 获取AI回复
            agent_response = result.get("response", "抱歉，我无法处理您的请求。")
            
            # 🆕 添加下一步建议到交互式聊天响应
            next_step_suggestion = generate_next_step_suggestion(
                user_message=user_input,
                agent_response=agent_response,
                tool_used=tool_used,
                tool_calls_history=tool_calls_history
            )
            agent_response_with_suggestion = agent_response + (next_step_suggestion if next_step_suggestion.strip() else "")
            
            print("\n🤖 Analysis Agent:")
            print("=" * 50)
            
            # 显示包含下一步建议的回复
            self.print_streaming(agent_response_with_suggestion, 0.02)
            
            print("=" * 50)
            
            # 显示简化的处理信息
            if tool_used:
                tool_names = [call["tool_call"]["tool_name"] for call in tool_calls_history]
                success_count = sum(1 for call in tool_calls_history if call["tool_result"]["success"])
                self.print_streaming(f"🔧 使用工具: {', '.join(tool_names)} ({success_count}/{len(tool_calls_history)}成功)", 0.02)
            
            self.print_streaming(f"⏱️ 处理时间: {processing_time:.2f}秒", 0.02)
            
            return agent_response, result
            
        except Exception as e:
            self.print_streaming(f"\n\n❌ 处理出错: {str(e)}", 0.02)
            self.print_streaming("</think>\n", 0.02)
            return None, None
    
    def start_chat(self):
        """开始交互式对话"""
        if not self.initialize_agent():
            return
        
        print("\n" + "="*60)
        print("🤖 Analysis Agent 交互式对话 (流式输出版本)")
        print("="*60)
        print("💡 可以询问:")
        print("   - 数据查询：'2025年1月电费是多少'")
        print("   - 知识搜索：'电费是怎么计算的'")
        print("   - 业务问题：'用电成本如何分析'")
        print("\n📝 输入 'quit', 'exit', '退出' 结束对话")
        print("📝 输入 'clear', '清空' 清空对话历史")
        print("📝 输入 'history', '历史' 查看对话历史")
        print("📝 输入 'demo', '演示' 查看演示问题")
        print(f"📄 日志文件: {log_filename}")
        print("="*60 + "\n")
        
        logger.info("🤖 Analysis Agent 交互式对话模式启动")
        
        while True:
            try:
                # 获取用户输入
                user_input = input("👤 您: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("\n👋 再见！感谢使用Analysis Agent!")
                    break
                
                if user_input.lower() in ['clear', '清空']:
                    self.conversation_history.clear()
                    self.print_streaming("✅ 对话历史已清空", 0.02)
                    continue
                
                if user_input.lower() in ['history', '历史']:
                    self.show_history()
                    continue
                
                if user_input.lower() in ['demo', '演示']:
                    self.show_demo_questions()
                    continue
                
                # 处理用户问题（流式输出）
                print()  # 空行
                agent_response, result = self.process_message_with_streaming(user_input)
                
                if agent_response and result:
                    # 保存到对话历史
                    self.conversation_history.append({
                        "role": "user",
                        "content": user_input
                    })
                    self.conversation_history.append({
                        "role": "assistant", 
                        "content": agent_response
                    })
                
                print("\n" + "-"*60)
                
            except KeyboardInterrupt:
                print("\n\n👋 检测到Ctrl+C，退出对话...")
                break
            except EOFError:
                print("\n\n👋 检测到EOF，退出对话...")
                break
    
    def show_demo_questions(self):
        """显示演示问题"""
        print("\n🎯 演示问题:")
        print("-" * 40)
        demo_questions = [
            "2025年1月的电费是多少？",
            "查询最近一个月的用电情况",
            "电费是怎么计算的？",
            "什么是阶梯电价？",
            "如何分析用电成本？",
            "用电量最高的是哪个月份？"
        ]
        
        for i, question in enumerate(demo_questions, 1):
            print(f"{i}. {question}")
        
        print("-" * 40)
        print("💡 提示：复制上面的问题直接使用，或者根据这些例子构造自己的问题")
    
    def show_history(self):
        """显示对话历史"""
        if not self.conversation_history:
            self.print_streaming("📝 暂无对话历史", 0.03)
            return
        
        print("\n📚 对话历史:")
        print("-" * 40)
        for i, entry in enumerate(self.conversation_history, 1):
            role = "👤 用户" if entry["role"] == "user" else "🤖 Agent"
            content = entry["content"]
            if len(content) > 100:
                content = content[:97] + "..."
            print(f"{i}. {role}: {content}")
        print("-" * 40)


def generate_next_step_suggestion(user_message: str, agent_response: str, tool_used: bool = False, tool_calls_history: list = None) -> str:
    """
    使用LLM根据实际数据动态生成3个智能的下一步建议
    
    Args:
        user_message: 用户原始问题
        agent_response: Agent的回复
        tool_used: 是否使用了工具
        tool_calls_history: 工具调用历史
    
    Returns:
        格式化的下一步建议字符串（包含3个建议）
    """
    import random
    
    # 🆕 智能检测：检查Agent响应是否已经包含了类似的建议内容
    def has_existing_suggestions(response: str) -> bool:
        """检测响应中是否已经包含建议内容"""
        suggestion_indicators = [
            # 直接的建议标识
            "下一步你可能会问", "您可能还想了解", "相关问题", "延伸问题", 
            "建议您", "您还可以", "推荐查询", "可以进一步",
            # 问号结尾的建议句式
            "是否需要", "要不要", "需要查看", "想了解",
            # 控制标签
            "<uni-chat-control>", "</uni-chat-control>",
            # 其他建议形式
            "还可以查询", "也可以关注", "建议关注", "可以对比"
        ]
        
        response_lower = response.lower()
        for indicator in suggestion_indicators:
            if indicator.lower() in response_lower:
                return True
        
        # 检查是否有多个问号（可能是连续的建议问题）
        question_marks = response.count('？') + response.count('?')
        if question_marks >= 2:
            return True
            
        return False
    
    # 如果Agent响应中已经包含了建议，跳过生成
    if has_existing_suggestions(agent_response):
        print(f"🔍 DEBUG: 检测到Agent响应中已包含建议内容，跳过重复添加")
        return ""  # 返回空字符串，不添加额外建议
    
    try:
        # 🆕 使用LLM动态生成智能建议
        from src.core.llm_factory import LLMFactory
        from langchain_core.messages import HumanMessage, SystemMessage
        
        # 创建一个专门用于生成建议的LLM实例 - 使用性能优化配置
        suggestion_llm = LLMFactory.create_chat_model(
            provider="local",
            temperature=PERFORMANCE_CONFIG["temperature"],  # 使用性能优化的温度
            max_tokens=PERFORMANCE_CONFIG["max_tokens"],    # 限制输出长度
            timeout=PERFORMANCE_CONFIG["timeout"],          # 使用优化的超时时间
            preserve_think_tags=False  # 关闭think标签，提高响应速度
        )
        
        # 🔧 构建丰富的上下文信息
        context_info = []
        context_info.append(f"**用户问题**: {user_message}")
        context_info.append(f"**Agent回复**: {agent_response[:1000]}...")  # 限制长度避免token过多
        
        # 添加工具使用情况的上下文
        if tool_used and tool_calls_history:
            context_info.append(f"**使用的工具**: 已调用了{len(tool_calls_history)}个工具")
            
            for i, tool_info in enumerate(tool_calls_history, 1):
                tool_name = tool_info["tool_call"]["tool_name"]
                success = tool_info["tool_result"]["success"]
                
                if tool_name == "integrated_sql" and success:
                    # 提取SQL查询的关键信息
                    result = tool_info["tool_result"]["result"]
                    if isinstance(result, dict):
                        execution_result = result.get("execution_result", {})
                        row_count = execution_result.get("row_count", 0)
                        columns = execution_result.get("columns", [])
                        context_info.append(f"- SQL查询{i}: 成功获取{row_count}行数据，包含字段{columns}")
                        
                        # 🔧 关键：提取实际数据用于智能分析
                        data = execution_result.get("data", [])
                        if data and len(data) > 0:
                            # 提取前几行数据作为样本
                            sample_data = data[:3]
                            context_info.append(f"- 数据样本: {sample_data}")
                
                elif tool_name == "knowledge_search" and success:
                    context_info.append(f"- 知识搜索{i}: 成功找到相关业务逻辑信息")
        
        context_text = "\n".join(context_info)
        
        # 🆕 简化的提示词以提高性能
        system_prompt = """你是管理咨询顾问，基于数据为企业高管提供决策建议。

任务：生成3个管理导向的后续分析建议，重点关注：
1. 成本优化和效率提升
2. 风险识别和预防
3. 对标分析和预测规划

要求：建议要具体可执行，服务于管理决策。

例如：2025年3月全省铁塔单站服务费是多少？生成的分析建议应当倾向于管理咨询，比如，根据本省当前情况，如何提升铁塔服务费管理水平？
输出格式：
**下一步你可能会问**
<uni-chat-control>第一个具体建议内容</uni-chat-control>
<uni-chat-control>第二个具体建议内容</uni-chat-control>
<uni-chat-control>第三个具体建议内容</uni-chat-control>"""

        user_prompt = f"""基于以下信息生成3个管理建议：

{context_text}

要求：
1. 结合具体数值分析
2. 关注成本优化、效率提升、风险控制
3. 建议要可执行，有管理价值

严格按照指定格式输出。"""

        # 调用LLM生成建议
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        response = suggestion_llm.invoke(messages)
        generated_suggestions = response.content if hasattr(response, 'content') else str(response)
        
        # 🔧 验证生成的建议格式
        if "**下一步你可能会问**" in generated_suggestions and "<uni-chat-control>" in generated_suggestions:
            print(f"🔍 DEBUG: LLM成功生成智能建议")
            return "\n" + generated_suggestions.strip()
        else:
            print(f"⚠️ DEBUG: LLM生成的建议格式不正确，使用备用方案")
            # 备用方案：生成通用建议
            return generate_fallback_suggestions(user_message, agent_response, tool_used)
            
    except Exception as e:
        print(f"❌ DEBUG: 生成智能建议失败: {e}")
        # 出错时使用备用方案
        return generate_fallback_suggestions(user_message, agent_response, tool_used)


def generate_fallback_suggestions(user_message: str, agent_response: str, tool_used: bool) -> str:
    """
    备用建议生成方案（基于规则的管理咨询导向版本）
    """
    import random
    
    # 简化的分类逻辑，但更强调管理价值
    user_lower = user_message.lower()
    
    if any(keyword in user_lower for keyword in ["电费", "铁塔", "共享率", "用电"]):
        suggestions = [
            "基于当前数据制定成本优化策略，识别降本增效的具体机会点？",
            "建立资源配置效率监控体系，预防潜在的运营风险？",
            "对标行业最佳实践，制定提升竞争优势的行动计划？"
        ]
    elif any(keyword in user_lower for keyword in ["数据", "统计", "分析"]):
        suggestions = [
            "从管理决策角度，如何将数据洞察转化为可执行的业务策略？",
            "建立关键指标预警机制，确保业务目标的达成？",
            "基于数据表现评估投资回报，优化资源分配决策？"
        ]
    else:
        suggestions = [
            "制定基于分析结果的管理改进措施，提升整体运营效率？",
            "建立持续监控机制，确保关键指标保持在目标范围内？",
            "从成本效益角度制定优先级，合理配置有限的管理资源？"
        ]
    
    # 格式化输出，确保管理咨询导向
    suggestion_lines = []
    for suggestion in suggestions:
        suggestion_lines.append(f"<uni-chat-control>{suggestion}</uni-chat-control>")
    
    return f"""
**下一步你可能会问**  
{chr(10).join(suggestion_lines)}"""


def main():
    """主函数 - 支持不同启动模式"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Analysis Agent服务器")
    parser.add_argument(
        "--mode", 
        choices=["server", "chat"], 
        default="server",
        help="启动模式: server(API服务器) 或 chat(交互式对话)"
    )
    parser.add_argument(
        "--host",
        default=settings.server_host,
        help=f"服务器主机地址 (仅server模式，默认: {settings.server_host})"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=settings.server_port,
        help=f"服务器端口 (仅server模式，默认: {settings.server_port})"
    )
    
    args = parser.parse_args()
    
    if args.mode == "chat":
        # 交互式对话模式
        chat = InteractiveAgentChat()
        chat.start_chat()
    else:
        # API服务器模式
        # 检查配置
        if not settings.local_llm_enabled:
            print("❌ 请先在.env文件中启用和配置本地LLM")
            return
        
        print(f"\n{'='*60}")
        print("🚀 Analysis Agent API 服务器启动中...")
        print(f"{'='*60}")
        print(f"📊 服务: Analysis Agent - 智能数据分析")
        print(f"🔧 版本: 1.0.0")
        print(f"🌐 服务地址: http://{args.host}:{args.port}")
        print(f"📖 API文档: http://{args.host}:{args.port}/docs")
        print(f"📈 性能统计: http://{args.host}:{args.port}/stats")
        print(f"⚡ 性能优化: 已启用")
        print(f"🌡️ Temperature: {PERFORMANCE_CONFIG['temperature']}")
        print(f"🔄 最大迭代: {PERFORMANCE_CONFIG['max_iterations']}次")
        print(f"⏱️ 超时设置: {PERFORMANCE_CONFIG['timeout']}秒")
        
        # 记录服务器启动时间
        app.state.start_time = time.time()

        try:
            # 预启动检查 - 检查关键服务连接
            check_service_connections()

            # 初始化Agent - 如果失败则停止启动
            print("🔧 正在初始化核心组件...")
            init_sql_agent()
            print("✅ 核心组件初始化完成")

        except Exception as e:
            print(f"\n❌ 服务启动失败: {e}")
            print("\n🔍 可能的原因:")
            print("  1. LLM服务连接失败")
            print("  2. 数据库连接失败")
            print("  3. Vanna服务连接失败")
            print("  4. 关键配置缺失或错误")
            print("\n💡 请检查配置并确保所有依赖服务正常运行")
            logger.error(f"❌ 服务启动失败: {e}")
            return  # 退出，不启动服务器

        logger.info("🚀 Analysis Agent API 服务器启动")
        logger.info(f"🌐 监听地址: {args.host}:{args.port}")

        # 启动服务器
        uvicorn.run(app, host=args.host, port=args.port, log_level="info")


if __name__ == "__main__":
    main() 