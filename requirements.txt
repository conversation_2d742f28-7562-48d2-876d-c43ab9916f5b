# Lang<PERSON>hain 核心包
langchain>=0.3.0,<0.4.0
langchain-openai>=0.3.0,<0.4.0
langchain-community>=0.3.0,<0.4.0
langchain-core>=0.3.0,<0.4.0
langchain-text-splitters>=0.3.0,<0.4.0
langchain-anthropic>=0.3.0,<0.4.0
langsmith>=0.4.0,<0.5.0

# 环境管理
python-dotenv>=1.0.0

# Web框架 - 使用更新版本以支持 numpy 2.x
streamlit>=1.39.0
fastapi>=0.100.0
uvicorn>=0.20.0

# HTTP客户端
httpx>=0.25.0
requests>=2.28.0

# 数据处理 - 使用兼容版本
pandas>=2.0.0
numpy>=1.26.2,<2.1.0
tiktoken>=0.7.0



# 向量数据库
langchain-chroma>=0.1.0
chromadb>=0.4.0
faiss-cpu>=1.7.0
sentence-transformers>=2.2.0

# AI模型API
openai>=1.0.0
anthropic>=0.8.0

# 数据库
redis>=5.0.0
pinecone-client>=3.0.0
weaviate-client>=3.20.0
pymongo>=4.0.0
psycopg2-binary>=2.9.0
pymysql==1.1.1

# 核心依赖
pydantic>=2.0.0
SQLAlchemy>=2.0.0
PyYAML>=6.0.0 

# Excel文件处理 (用于 src/expense_analysis 模块)
openpyxl>=3.1.0