2025-08-18 10:23:32,154 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:23:32,156 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 10:23:32,157 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 10:23:32,163 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 10:23:33,347 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:23:33,798 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-18 10:23:33,800 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-18 10:23:33,801 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-18 10:27:30,638 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:27:30,640 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:27:30,641 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:27:30,641 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:27:30,642 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:27:30,643 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:27:30,643 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:27:30,644 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:27:30,963 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:27:31,443 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:27:31,596 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-18 10:27:31,598 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-18 10:27:31,598 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-18 10:31:35,811 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:31:35,813 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:31:35,813 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:31:35,814 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:31:35,815 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:31:36,109 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:31:36,150 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:31:37,148 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 10:31:37,148 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 10:31:37,177 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 10:31:37,178 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 10:31:37,178 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 10:31:37,179 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 10:31:37,179 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 10:31:37,180 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:31:37,181 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:31:37,182 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 10:31:37,182 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 10:31:37,392 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:31:37,393 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:31:37,393 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:37:43,135 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:37:43,137 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:37:43,138 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:37:43,138 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:37:43,140 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:37:43,140 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:37:43,141 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:37:43,141 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:37:43,473 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:37:43,913 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:37:44,190 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 10:37:44,191 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 10:37:44,207 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 10:37:44,207 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 10:37:44,208 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 10:37:44,209 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 10:37:44,209 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 10:37:44,210 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:37:44,210 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:37:44,211 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 10:37:44,211 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 10:37:44,255 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:37:44,256 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:37:44,257 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:11:40,043 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:11:40,044 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:11:40,044 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:11:40,045 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:11:40,047 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:11:40,047 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:11:40,048 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:11:40,048 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:11:40,695 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 11:11:41,553 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:11:42,305 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:11:42,306 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:11:42,324 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:11:42,326 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:11:42,327 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:11:42,327 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:11:42,328 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:11:42,328 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:11:42,453 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:11:42,454 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:11:42,455 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:12:16,107 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:12:16,109 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:12:16,109 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:12:16,110 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:12:16,111 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:12:16,112 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:12:16,112 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:12:16,113 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:12:16,412 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 11:12:16,850 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:12:17,167 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:12:17,168 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:12:17,185 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:12:17,185 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:12:17,186 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:12:17,186 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:12:17,187 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:12:17,187 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:12:17,188 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:12:17,189 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:12:17,189 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:12:17,241 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:12:17,242 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:12:17,242 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:47:37,537 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:47:37,539 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:47:37,539 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:37,540 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:47:37,542 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:47:38,862 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,863 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,863 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,864 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 11:47:39,339 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:47:39,630 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:47:39,631 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:47:39,648 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:47:39,649 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:47:39,650 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:47:39,650 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:47:39,651 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:47:39,651 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:47:39,652 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:47:39,652 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:47:39,653 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:47:39,712 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:47:39,713 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:47:39,713 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:05,498 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:05,501 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 12:35:05,502 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:05,502 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 12:35:05,503 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 12:35:05,504 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:05,505 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 12:35:05,505 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 12:35:06,969 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,970 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,970 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,971 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 12:35:07,425 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:35:07,703 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:35:07,703 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:35:07,721 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:35:07,721 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:35:07,722 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:35:07,722 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:35:07,723 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:35:07,723 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:07,724 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:07,725 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:35:07,725 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 12:35:07,774 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:07,775 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:07,776 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:40,262 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:40,264 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 12:35:40,264 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:40,265 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 12:35:40,266 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 12:35:40,266 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:40,267 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 12:35:40,267 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 12:35:41,659 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,660 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,660 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,661 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 12:35:42,157 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:35:42,421 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:35:42,421 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:35:42,439 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:35:42,440 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:35:42,440 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:35:42,441 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:35:42,441 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:35:42,442 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:42,443 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:42,443 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:35:42,444 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 12:35:42,495 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:42,495 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:42,496 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
