2025-08-18 10:23:32,154 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:23:32,156 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 10:23:32,157 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 10:23:32,163 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 10:23:33,347 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:23:33,798 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-18 10:23:33,800 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-18 10:23:33,801 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-18 10:27:30,638 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:27:30,640 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:27:30,641 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:27:30,641 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:27:30,642 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:27:30,643 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:27:30,643 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:27:30,644 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:27:30,963 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:27:31,443 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:27:31,596 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-18 10:27:31,598 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-18 10:27:31,598 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
