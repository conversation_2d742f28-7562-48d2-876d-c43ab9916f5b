# 测试环境配置
# Testing Environment Configuration

# ================================
# 应用基础配置
# ================================
APP_NAME=AI Chat LangChain (测试环境)
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# ================================
# 服务器配置
# ================================
SERVER_HOST=0.0.0.0
SERVER_PORT=8001

# ================================
# 本地LLM配置 (测试环境)
# ================================
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=sk-test-api-key
LOCAL_LLM_MODEL_URL=http://test-llm-server:8077/dify/i/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3_30B_A3B
LOCAL_LLM_TEMPERATURE=0.5
LOCAL_LLM_TIMEOUT=30

# ================================
# 嵌入模型配置 (测试环境)
# ================================
LOCAL_BGE_ENABLED=true
LOCAL_BGE_API_URL=http://test-embedding-server:8080/v1/embeddings
LOCAL_BGE_API_KEY=test_embedding_api_key
LOCAL_BGE_MODEL_NAME=bge-large-zh-v1.5

# ================================
# 数据库配置 (测试环境)
# ================================
MYSQL_HOST=test-mysql-server
MYSQL_PORT=3306
MYSQL_USER=test_user
MYSQL_PASSWORD=test_password
MYSQL_DATABASE=analysis_test
MYSQL_CHARSET=utf8mb4

POSTGRESQL_HOST=test-postgres-server
POSTGRESQL_PORT=5432
POSTGRESQL_USER=test_user
POSTGRESQL_PASSWORD=test_password
POSTGRESQL_DATABASE=analysis_test

# ================================
# 向量数据库配置 (测试环境)
# ================================
REDIS_URL=redis://test-redis-server:6379/1
MONGODB_URL=mongodb://test-mongo-server:27017
MONGODB_DB_NAME=langchain_test

# ================================
# LangSmith配置 (测试环境)
# ================================
LANGCHAIN_TRACING_V2=true
LANGCHAIN_PROJECT=ai-chat-langchain-test

# ================================
# 知识库查询服务配置 (测试环境)
# ================================
KNOWLEDGE_SEARCH_API_URL=http://test-knowledge-server:8081/v1/workflows/run
KNOWLEDGE_SEARCH_API_KEY=app-test-knowledge-key

# ================================
# SQL生成服务配置 (测试环境)
# ================================
SQL_GENERATOR_API_URL=http://test-sql-server:5000/api/v0/generate_sql
SQL_GENERATOR_API_KEY=vn_test_api_key

# ================================
# 省份数据库映射配置 (测试环境)
# ================================
PROVINCE_DATABASE_MAPPING=GZ:analysis_gz_test:贵州,NX:analysis_nx_test:宁夏,HE:analysis_he_test:河北

# ================================
# 远程Embedding模型配置 (测试环境)
# ================================
REMOTE_EMBEDDING_ENABLED=true
REMOTE_EMBEDDING_URL=http://test-embedding-server:8001/embeddings
REMOTE_EMBEDDING_API_KEY=test-embedding-api-key
REMOTE_EMBEDDING_MODEL=text-embedding-ada-002
