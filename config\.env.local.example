# 本地覆盖配置示例
# Local Override Configuration Example
# 
# 使用方法：
# 1. 复制此文件为 .env.local
# 2. 修改需要覆盖的配置项
# 3. .env.local 会覆盖环境特定配置，且不会被git跟踪
#
# 注意：.env.local 优先级最高，会覆盖所有其他配置

# ================================
# 开发者个人配置覆盖示例
# ================================

# 覆盖调试级别
# LOG_LEVEL=DEBUG

# 覆盖本地数据库配置
# MYSQL_HOST=localhost
# MYSQL_USER=your_local_user
# MYSQL_PASSWORD=your_local_password

# 覆盖本地LLM配置
# LOCAL_LLM_MODEL_URL=http://localhost:8077/dify/i/v1/chat/completions
# LOCAL_LLM_API_KEY=your_local_api_key

# 覆盖服务器端口
# SERVER_PORT=8002

# 覆盖Redis配置
# REDIS_URL=redis://localhost:6379/9
