"""
工具调用Agent
结合LLM和工具，实现智能任务执行
"""

import json
import re
from typing import List, Dict, Any, Optional, Union
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

from ..tools import get_all_tools
from ..tools.base_tools import BaseTool


class ToolCallingAgent:
    """具有工具调用能力的Agent"""
    
    def __init__(self, 
                 llm: BaseLanguageModel,
                 tools: Optional[List[BaseTool]] = None,
                 system_prompt: Optional[str] = None):
        """
        初始化Agent
        
        Args:
            llm: 语言模型
            tools: 可用工具列表，如果为None则使用默认工具
            system_prompt: 系统提示词
        """
        self.llm = llm
        self.tools = tools or get_all_tools()
        self.system_prompt = system_prompt or self._default_system_prompt()
        
        # 创建工具映射
        self.tool_map = {tool.name: tool for tool in self.tools}
    
    def _default_system_prompt(self) -> str:
        """默认系统提示词"""
        tool_descriptions = []
        for tool in self.tools:
            schema = tool.get_schema()
            tool_descriptions.append(f"""
{tool.name}: {tool.description}
参数: {json.dumps(schema['function']['parameters'], ensure_ascii=False, indent=2)}
""")
        
        tools_info = "\n".join(tool_descriptions)
        
        return f"""你是一个智能助手，可以使用以下工具来帮助用户解决问题：

{tools_info}

使用工具的格式：
当你需要使用工具时，请使用以下JSON格式：
```json
{{
    "tool_name": "工具名称",
    "parameters": {{
        "参数名": "参数值"
    }}
}}
```

重要说明：
1. 只有在确实需要使用工具时才调用工具
2. 工具调用必须严格按照上述JSON格式
3. 参数必须完全匹配工具定义的参数名称
4. 如果用户的问题可以直接回答，不需要调用工具
5. 每次只能调用一个工具

请根据用户的需求，判断是否需要使用工具，并给出适当的回应。"""
    
    def process_message(self, message: str, conversation_history: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        处理用户消息
        
        Args:
            message: 用户消息
            conversation_history: 对话历史
            
        Returns:
            包含回复和工具调用结果的字典
        """
        try:
            # 构建消息列表
            messages = [SystemMessage(content=self.system_prompt)]
            
            # 添加对话历史
            if conversation_history:
                for entry in conversation_history:
                    if entry.get("role") == "user":
                        messages.append(HumanMessage(content=entry["content"]))
                    elif entry.get("role") == "assistant":
                        messages.append(AIMessage(content=entry["content"]))
            
            # 添加当前用户消息
            messages.append(HumanMessage(content=message))
            
            # 获取LLM回复
            response = self.llm.invoke(messages)
            response_text = response.content if hasattr(response, 'content') else str(response)
            
            # 检查是否需要调用工具
            tool_call = self._extract_tool_call(response_text)
            
            if tool_call:
                # 执行工具调用
                tool_result = self._execute_tool(tool_call)
                
                # 生成基于工具结果的最终回复
                final_response = self._generate_final_response(message, tool_call, tool_result, messages)
                
                return {
                    "response": final_response,
                    "tool_used": True,
                    "tool_call": tool_call,
                    "tool_result": tool_result,
                    "raw_response": response_text
                }
            else:
                return {
                    "response": response_text,
                    "tool_used": False,
                    "tool_call": None,
                    "tool_result": None,
                    "raw_response": response_text
                }
                
        except Exception as e:
            return {
                "response": f"处理消息时发生错误: {str(e)}",
                "tool_used": False,
                "tool_call": None,
                "tool_result": None,
                "error": str(e)
            }
    
    def _extract_tool_call(self, response_text: str) -> Optional[Dict[str, Any]]:
        """从回复中提取工具调用"""
        try:
            # 查找JSON代码块
            json_pattern = r'```json\s*(\{.*?\})\s*```'
            matches = re.findall(json_pattern, response_text, re.DOTALL)
            
            if matches:
                tool_call_str = matches[0]
                tool_call = json.loads(tool_call_str)
                
                # 验证工具调用格式
                if "tool_name" in tool_call and "parameters" in tool_call:
                    return tool_call
            
            return None
            
        except (json.JSONDecodeError, Exception):
            return None
    
    def _execute_tool(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具调用"""
        try:
            tool_name = tool_call["tool_name"]
            parameters = tool_call["parameters"]
            
            if tool_name not in self.tool_map:
                return {
                    "success": False,
                    "error": f"未知工具: {tool_name}",
                    "result": None
                }
            
            tool = self.tool_map[tool_name]
            result = tool.call(**parameters)
            
            return {
                "success": result.success,
                "result": result.result,
                "error": result.error
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"工具执行错误: {str(e)}",
                "result": None
            }
    
    def _generate_final_response(self, 
                               original_message: str,
                               tool_call: Dict[str, Any],
                               tool_result: Dict[str, Any],
                               messages: List) -> str:
        """基于工具结果生成最终回复"""
        try:
            # 构建包含工具结果的提示
            tool_result_prompt = f"""
用户问题: {original_message}

我使用了工具 {tool_call['tool_name']} 来帮助回答，结果如下：
- 执行成功: {tool_result['success']}
- 结果: {tool_result['result']}
- 错误信息: {tool_result.get('error', '无')}

请基于这个工具执行结果，给用户一个清晰、有帮助的回答。不要重复显示工具调用的JSON格式。
"""
            
            messages.append(HumanMessage(content=tool_result_prompt))
            
            response = self.llm.invoke(messages)
            return response.content if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            # 如果生成最终回复失败，返回基础回复
            if tool_result['success']:
                return f"我使用了{tool_call['tool_name']}工具来处理您的请求。结果：{tool_result['result']}"
            else:
                return f"抱歉，使用{tool_call['tool_name']}工具时出现了错误：{tool_result.get('error', '未知错误')}"
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return [tool.to_dict() for tool in self.tools] 