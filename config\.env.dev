# 开发环境配置
# Development Environment Configuration

# ================================
# 应用基础配置
# ================================
APP_NAME=AI Chat LangChain (开发环境)
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=DEBUG

# ================================
# 服务器配置
# ================================
SERVER_HOST=127.0.0.1
SERVER_PORT=8001

# ================================
# 本地LLM配置 (开发环境)
# ================================
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=sk-7aAUUpWbGBOr3TYdWuJVlRhTYumzlb7Yw8nNoSIgx1WOfUg5
LOCAL_LLM_MODEL_URL=http://**********:8077/dify/i/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3_30B_A3B
LOCAL_LLM_TEMPERATURE=0.7
LOCAL_LLM_TIMEOUT=60

# ================================
# 嵌入模型配置 (开发环境)
# ================================
LOCAL_BGE_ENABLED=false
# LOCAL_BGE_API_URL=http://your_embedding_service/v1/embeddings
# LOCAL_BGE_API_KEY=your_embedding_api_key
# LOCAL_BGE_MODEL_NAME=bge-large-zh-v1.5

# ================================
# 数据库配置 (开发环境 - 本地数据库)
# ================================
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=dev_password
MYSQL_DATABASE=analysis_dev
MYSQL_CHARSET=utf8mb4

POSTGRESQL_HOST=localhost
POSTGRESQL_PORT=5432
POSTGRESQL_USER=postgres
POSTGRESQL_PASSWORD=dev_password
POSTGRESQL_DATABASE=analysis_dev

# ================================
# 向量数据库配置 (开发环境)
# ================================
REDIS_URL=redis://localhost:6379/0
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB_NAME=langchain_dev

# ================================
# LangSmith配置 (开发环境)
# ================================
LANGCHAIN_TRACING_V2=true
LANGCHAIN_PROJECT=ai-chat-langchain-dev

# ================================
# 知识库查询服务配置 (开发环境)
# ================================
KNOWLEDGE_SEARCH_API_URL=http://***********:8081/v1/workflows/run
KNOWLEDGE_SEARCH_API_KEY=app-yBG3HXjw9NiGcxXzJ4QmHEFD

# ================================
# SQL生成服务配置 (开发环境)
# ================================
SQL_GENERATOR_API_URL=http://***********:5000/api/v0/generate_sql
SQL_GENERATOR_API_KEY=vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1

# ================================
# 省份数据库映射配置 (开发环境 - 简化配置)
# ================================
PROVINCE_DATABASE_MAPPING=GZ:analysis_gz_dev:贵州,NX:analysis_nx_dev:宁夏

# ================================
# 远程Embedding模型配置 (开发环境)
# ================================
REMOTE_EMBEDDING_ENABLED=false
REMOTE_EMBEDDING_URL=http://localhost:8001/embeddings
REMOTE_EMBEDDING_API_KEY=dev-api-key
REMOTE_EMBEDDING_MODEL=text-embedding-ada-002
