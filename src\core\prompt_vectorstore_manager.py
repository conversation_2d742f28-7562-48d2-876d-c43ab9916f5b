"""
复杂提示词向量数据库管理器
用于存储和检索复杂的处理流程和提示词
"""

import os
import json
import uuid
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings
from langchain_chroma import Chroma
from src.core.llm_factory import LLMFactory
from src.config import settings

logger = logging.getLogger(__name__)


@dataclass
class ComplexPrompt:
    """复杂提示词数据结构"""
    id: str
    title: str
    description: str
    trigger_questions: List[str]  # 触发问题列表
    processing_steps: List[str]  # 处理步骤
    response_format: str  # 回复格式要求
    priority: int = 1  # 优先级，数字越大优先级越高
    no_think_mode: bool = False  # 是否启用极速模式（关闭think标签）
    created_at: str = None
    updated_at: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        if self.updated_at is None:
            self.updated_at = datetime.now().isoformat()


class PromptVectorStoreManager:
    """复杂提示词向量数据库管理器"""
    
    def __init__(self, 
                 persist_directory: str = "./data/prompt_chroma_db",
                 collection_name: str = "complex_prompts"):
        """
        初始化管理器
        
        Args:
            persist_directory: 持久化目录
            collection_name: 集合名称
        """
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        self.embeddings = None
        self.vectorstore = None
        
        # 确保目录存在
        os.makedirs(persist_directory, exist_ok=True)
        
        # 初始化嵌入模型和向量存储
        self._initialize_vectorstore()
    
    def _initialize_vectorstore(self):
        """初始化向量存储"""
        try:
            # 创建嵌入模型 - 优先使用配置的embedding模型
            if settings.local_bge_enabled:
                logger.info("🔧 使用本地BGE embedding模型")
                self.embeddings = LLMFactory.create_embeddings(provider="local_bge")
            elif settings.remote_embedding_enabled:
                logger.info("🌐 使用远程embedding模型")
                logger.info(f"📡 服务地址: {settings.remote_embedding_url}")
                logger.info(f"🤖 模型名称: {settings.remote_embedding_model}")

                # 导入并使用远程embedding类
                from src.core.remote_embeddings import create_remote_embeddings
                self.embeddings = create_remote_embeddings()

                # 测试连接
                if hasattr(self.embeddings, 'test_connection'):
                    if not self.embeddings.test_connection():
                        logger.warning("⚠️ 远程embedding服务连接测试失败，但继续初始化")
            else:
                logger.warning("⚠️ 未配置embedding模型，使用占位符embedding")
                logger.info("💡 请在.env中配置:")
                logger.info("   REMOTE_EMBEDDING_ENABLED=true")
                logger.info("   REMOTE_EMBEDDING_URL=您的embedding服务地址")
                logger.info("   REMOTE_EMBEDDING_API_KEY=您的API密钥")
                logger.info("   REMOTE_EMBEDDING_MODEL=您的模型名称")

                # 创建占位符embedding，避免系统崩溃
                from langchain_core.embeddings import Embeddings
                class PlaceholderEmbeddings(Embeddings):
                    def embed_documents(self, texts):
                        logger.warning("⚠️ 使用占位符embedding，请配置真实的embedding服务")
                        return [[0.0] * 384 for _ in texts]  # 返回固定维度的零向量
                    def embed_query(self, text):
                        logger.warning("⚠️ 使用占位符embedding，请配置真实的embedding服务")
                        return [0.0] * 384
                self.embeddings = PlaceholderEmbeddings()

            # 创建或加载向量存储
            self.vectorstore = Chroma(
                embedding_function=self.embeddings,
                persist_directory=self.persist_directory,
                collection_name=self.collection_name
            )

            # 检查数据库中的数据
            try:
                collection = self.vectorstore._collection
                count = collection.count()
                logger.info(f"✅ 复杂提示词向量数据库初始化成功: {self.persist_directory}")
                logger.info(f"📊 数据库中现有复杂提示词数量: {count}")

                # 如果有数据，显示一些示例
                if count > 0:
                    results = collection.peek(limit=3)
                    logger.info(f"📝 数据库中的示例数据: {len(results.get('ids', []))} 条")
                    for i, doc_id in enumerate(results.get('ids', [])[:3]):
                        metadata = results.get('metadatas', [{}])[i]
                        title = metadata.get('title', 'Unknown')
                        logger.info(f"  - {doc_id}: {title}")
                else:
                    logger.warning("⚠️ 数据库为空，没有复杂提示词数据")

            except Exception as e:
                logger.warning(f"⚠️ 无法检查数据库状态: {e}")

            logger.info(f"✅ 复杂提示词向量数据库初始化完成")

        except Exception as e:
            logger.error(f"❌ 向量数据库初始化失败: {e}")
            raise
    
    def add_complex_prompt(self, prompt: ComplexPrompt) -> bool:
        """
        添加复杂提示词 - 为每个触发问题单独创建向量

        Args:
            prompt: 复杂提示词对象

        Returns:
            bool: 是否添加成功
        """
        try:
            documents = []

            # 为每个触发问题创建单独的文档
            for i, question in enumerate(prompt.trigger_questions):
                # 构建元数据
                metadata = {
                    "id": prompt.id,
                    "question_index": i,  # 问题索引
                    "title": prompt.title,
                    "description": prompt.description,
                    "trigger_questions": json.dumps(prompt.trigger_questions, ensure_ascii=False),
                    "processing_steps": json.dumps(prompt.processing_steps, ensure_ascii=False),
                    "response_format": prompt.response_format,
                    "priority": prompt.priority,
                    "no_think_mode": prompt.no_think_mode,
                    "created_at": prompt.created_at,
                    "updated_at": prompt.updated_at,
                    "trigger_question": question  # 当前触发问题
                }

                # 创建文档，page_content就是单个触发问题
                document = Document(
                    page_content=question,
                    metadata=metadata
                )
                documents.append(document)

            # 批量添加到向量存储
            self.vectorstore.add_documents(documents)

            logger.info(f"✅ 成功添加复杂提示词: {prompt.title} (ID: {prompt.id}, {len(prompt.trigger_questions)}个触发问题)")
            return True

        except Exception as e:
            logger.error(f"❌ 添加复杂提示词失败: {e}")
            return False
    
    def search_complex_prompts(self,
                             query: str,
                             k: int = 10,  # 增加搜索数量，因为可能有重复的提示词
                             score_threshold: float = 0.3) -> List[Tuple[ComplexPrompt, float]]:
        """
        搜索匹配的复杂提示词 - 基于触发问题匹配

        Args:
            query: 查询文本
            k: 返回结果数量
            score_threshold: 相似度阈值

        Returns:
            List[Tuple[ComplexPrompt, float]]: 匹配的提示词和最高相似度分数
        """
        try:
            # 首先检查数据库中是否有数据
            collection = self.vectorstore._collection
            total_count = collection.count()
            logger.info(f"🔍 开始搜索复杂提示词，数据库中共有 {total_count} 条数据")
            logger.info(f"🔍 搜索查询: '{query}', k={k}, score_threshold={score_threshold}")

            if total_count == 0:
                logger.warning("⚠️ 数据库为空，无法进行搜索")
                return []

            # 执行相似度搜索，搜索更多结果
            results = self.vectorstore.similarity_search_with_score(query, k=k*3)
            logger.info(f"🔍 向量搜索返回 {len(results)} 个原始结果")

            # 用字典收集每个提示词的最高相似度
            prompt_scores = {}

            for doc, distance in results:
                similarity = 1.0 - distance  # 转换为相似度

                # 过滤低相似度结果
                if similarity >= score_threshold:
                    prompt_id = doc.metadata['id']
                    trigger_question = doc.metadata['trigger_question']

                    # 记录每个提示词的最高相似度
                    if prompt_id not in prompt_scores or similarity > prompt_scores[prompt_id][1]:
                        prompt = self._document_to_complex_prompt(doc)
                        prompt_scores[prompt_id] = (prompt, similarity, trigger_question)

            # 转换为列表并排序
            matched_prompts = []
            for prompt_id, (prompt, similarity, trigger_question) in prompt_scores.items():
                matched_prompts.append((prompt, similarity))
                logger.info(f"🎯 匹配: '{trigger_question}' → {prompt.title} (相似度: {similarity:.1%})")

            # 按相似度优先排序，优先级作为次要因素
            matched_prompts.sort(key=lambda x: (x[1], x[0].priority), reverse=True)
            matched_prompts = matched_prompts[:k]

            logger.info(f"🔍 搜索到 {len(matched_prompts)} 个匹配的复杂提示词")
            return matched_prompts

        except Exception as e:
            logger.error(f"❌ 搜索复杂提示词失败: {e}")
            return []
    
    def get_all_prompts(self) -> List[ComplexPrompt]:
        """获取所有复杂提示词 - 去重处理"""
        try:
            # 获取所有文档
            collection = self.vectorstore._collection
            total_count = collection.count()
            logger.info(f"📊 获取所有复杂提示词，数据库中共有 {total_count} 条数据")

            results = collection.get()
            logger.info(f"📊 实际获取到 {len(results.get('metadatas', []))} 条元数据")

            # 用字典去重，因为每个提示词可能有多个触发问题
            prompts_dict = {}

            if results and 'metadatas' in results:
                for metadata in results['metadatas']:
                    prompt_id = metadata['id']

                    # 如果还没有这个提示词，创建它
                    if prompt_id not in prompts_dict:
                        prompt = ComplexPrompt(
                            id=metadata['id'],
                            title=metadata['title'],
                            description=metadata['description'],
                            trigger_questions=json.loads(metadata['trigger_questions']),
                            processing_steps=json.loads(metadata['processing_steps']),
                            response_format=metadata['response_format'],
                            priority=metadata['priority'],
                            no_think_mode=metadata.get('no_think_mode', False),
                            created_at=metadata['created_at'],
                            updated_at=metadata['updated_at']
                        )
                        prompts_dict[prompt_id] = prompt

            return list(prompts_dict.values())

        except Exception as e:
            logger.error(f"❌ 获取所有提示词失败: {e}")
            return []
    
    def delete_prompt(self, prompt_id: str) -> bool:
        """删除复杂提示词 - 删除所有相关的触发问题"""
        try:
            # 根据ID删除所有相关文档
            collection = self.vectorstore._collection
            collection.delete(where={"id": prompt_id})

            logger.info(f"✅ 成功删除复杂提示词: {prompt_id}")
            return True

        except Exception as e:
            logger.error(f"❌ 删除复杂提示词失败: {e}")
            return False
    
    def update_prompt(self, prompt: ComplexPrompt) -> bool:
        """更新复杂提示词"""
        try:
            # 先删除旧的
            self.delete_prompt(prompt.id)
            
            # 更新时间戳
            prompt.updated_at = datetime.now().isoformat()
            
            # 重新添加
            return self.add_complex_prompt(prompt)
            
        except Exception as e:
            logger.error(f"❌ 更新复杂提示词失败: {e}")
            return False
    

    
    def _document_to_complex_prompt(self, doc: Document) -> ComplexPrompt:
        """从文档转换为ComplexPrompt对象"""
        metadata = doc.metadata
        return ComplexPrompt(
            id=metadata['id'],
            title=metadata['title'],
            description=metadata['description'],
            trigger_questions=json.loads(metadata['trigger_questions']),
            processing_steps=json.loads(metadata['processing_steps']),
            response_format=metadata['response_format'],
            priority=metadata['priority'],
            no_think_mode=metadata.get('no_think_mode', False),
            created_at=metadata['created_at'],
            updated_at=metadata['updated_at']
        )


# 全局实例
_prompt_vectorstore_manager = None

def get_prompt_vectorstore_manager() -> PromptVectorStoreManager:
    """获取全局复杂提示词向量数据库管理器实例"""
    global _prompt_vectorstore_manager
    if _prompt_vectorstore_manager is None:
        _prompt_vectorstore_manager = PromptVectorStoreManager()
    return _prompt_vectorstore_manager
