# Analysis Agent - 智能数据分析系统

基于LangChain的智能数据分析系统，专门用于自然语言到SQL查询的智能转换和数据分析。

## ✨ 核心功能

- 🤖 **智能SQL生成**: 自然语言转SQL查询
- 🏠 **本地模型支持**: 支持本地部署的LLM（如Qwen系列）
- 📊 **多数据库支持**: 支持MySQL、PostgreSQL等多种数据库
- 🌍 **省份感知查询**: 自动识别省份，选择对应数据库
- 🔍 **向量增强检索**: 基于ChromaDB的复杂提示词匹配
- 🌐 **OpenAI兼容API**: 可直接在Dify等平台使用
- 🛠️ **工具调用能力**: 支持多种工具的智能调用
- ⚙️ **灵活配置**: 支持环境变量和多环境配置管理

## 📖 完整文档

**请查看 [COMPREHENSIVE_GUIDE.md](./COMPREHENSIVE_GUIDE.md) 获取完整的使用指南**

该文档包含：
- 🚀 快速开始指南
- ⚙️ 详细配置说明
- 🔧 API使用方法
- 🌍 省份数据库映射
- 🔍 向量增强系统
- 🔌 Dify集成指南
- 🛠️ 工具调用说明
- 📈 性能优化建议
- 🚨 常见问题解答

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

```bash
cp env.example .env
# 编辑 .env 文件，配置你的LLM和数据库信息
```

**配置管理特性**：
- 🔧 **统一配置**: 所有配置通过环境变量管理
- 🌍 **多环境支持**: 支持开发、测试、生产环境配置
- 🔒 **安全管理**: 敏感信息通过环境变量注入

### 3. 启动服务

```bash
# 查看配置
python start_analysis_agent_server.py --show-config

# 启动服务器
python start_analysis_agent_server.py
```

### 4. 访问服务

- **API文档**: http://localhost:8001/docs
- **管理界面**: http://localhost:8001/admin
- **健康检查**: http://localhost:8001/health

## 🏗️ 项目结构

```
analysis-agent/
├── src/                          # 核心源码
│   ├── config/                   # 主配置模块
│   │   ├── settings.py          # 统一配置管理
│   │   └── database_config.py   # 数据库配置
│   ├── expense_analysis/         # 费用分析模块
│   │   ├── config/              # 费用分析专用配置
│   │   │   ├── model_config.py  # 模型配置（支持多环境）
│   │   │   └── .env.expense.*   # 环境特定配置
│   │   └── ...
│   └── ...
├── .env                         # 主环境配置
├── env.example                  # 配置模板
└── docs/                        # 文档目录
```

## 📚 更多信息

- **完整使用指南**: [COMPREHENSIVE_GUIDE.md](./COMPREHENSIVE_GUIDE.md)
- **配置管理指南**: [docs/configuration_guide.md](./docs/configuration_guide.md)
- **费用分析模块**: [src/expense_analysis/README.md](./src/expense_analysis/README.md)

## 📄 许可证

本项目采用 MIT 许可证。