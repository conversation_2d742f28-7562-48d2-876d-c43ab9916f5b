"""
增强版工具调用Agent
支持多步骤工具调用和智能工作流
"""

import json
import re
import time
import logging
from typing import List, Dict, Any, Optional, Union
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

from ..tools import get_all_tools
from ..tools.base_tools import BaseTool

# 设置日志记录器
logger = logging.getLogger(__name__)


class EnhancedToolCallingAgent:
    """支持多步骤工具调用的增强版Agent"""
    
    def __init__(self, 
                 llm: BaseLanguageModel,
                 tools: Optional[List[BaseTool]] = None,
                 system_prompt: Optional[str] = None,
                 max_iterations: int = 5,
                 enable_vector_guidance: bool = False,
                 vector_db_config: Optional[Dict[str, Any]] = None):
        """
        初始化Agent
        
        Args:
            llm: 语言模型
            tools: 可用工具列表，如果为None则使用默认工具
            system_prompt: 系统提示词
            max_iterations: 最大迭代次数，防止无限循环
            enable_vector_guidance: 是否启用向量数据库指导
            vector_db_config: 向量数据库配置
        """
        self.llm = llm
        self.tools = tools or get_all_tools()
        self.system_prompt = system_prompt or self._default_system_prompt()
        self.max_iterations = max_iterations
        self.enable_vector_guidance = enable_vector_guidance
        self.vector_db_config = vector_db_config or {}
        
        # 创建工具映射
        self.tool_map = {tool.name: tool for tool in self.tools}
    
    def _default_system_prompt(self) -> str:
        """默认系统提示词"""
        tool_descriptions = []
        for tool in self.tools:
            schema = tool.get_schema()
            tool_descriptions.append(f"""
{tool.name}: {tool.description}
参数: {json.dumps(schema['function']['parameters'], ensure_ascii=False, indent=2)}
""")
        
        tools_info = "\n".join(tool_descriptions)
        
        return f"""你是一个智能数据分析助手，可以使用以下工具来帮助用户解决问题：

{tools_info}

使用工具的格式：
当你需要使用工具时，请使用以下JSON格式：
```json
{{
    "tool_name": "工具名称",
    "parameters": {{
        "参数名": "参数值"
    }}
}}
```

🔧 **工具选择指南：**

**1. integrated_sql工具** - 用于查询具体的数据，如：
   - 电费、用水量、用电量等具体数值查询
   - 时间范围内的统计数据（如"2025年1月电费"、"最近一个月数据"）
   - 数据对比和分析（如"去年同期对比"）
   - 各种指标的具体数值（如"铁塔共享率"、"用电负荷"）
   - **用户明确提到"从数据库查询"、"查询数据"等关键词**
   - 任何需要从数据库获取实际数据的问题

**2. knowledge_search工具** - 用于查询业务逻辑和概念解释，如：
   - 指标计算方法和公式说明（如"电费是怎么计算的"）
   - 业务规则和流程解释（如"什么是阶梯电价"）
   - 系统功能和操作指南
   - 概念定义和背景知识

🎯 **关键判断原则：**
- **数据类问题**：用户问"是多少"、"有多少"、"查询XX数据" → 使用integrated_sql
- **概念类问题**：用户问"是什么"、"怎么计算"、"为什么" → 使用knowledge_search
- **用户明确指定**：当用户说"从数据库查询"、"查数据库"时，必须使用integrated_sql
- **指标名称**：铁塔共享率、用电负荷、电费金额等都是需要查询具体数值的，应使用integrated_sql

❗ **重要提醒：**
1. 优先根据用户的明确表述选择工具
2. 当用户提到"数据库"、"查询数据"等关键词时，优先使用integrated_sql
3. 工具调用必须严格按照JSON格式
4. 参数必须完全匹配工具定义的参数名称
5. **保持用户原始表述**：在调用工具时，请尽量保持用户原始问题的用词，不要随意替换或"优化"用户的表述（如：用户说"用电成本"就用"用电成本"，不要改成"电费"）
6. **智能多步骤调用策略**：
   - 第一次工具调用应该基于用户原始问题的直接理解
   - 只有在第一次结果明显不完整或需要补充信息时才进行第二次调用
   - 第二次调用必须有明确的改进目标（如：更精确的时间范围、不同的数据维度、补充相关指标等）
   - 避免为了"完善结果"而进行无意义的重复查询
7. **多工具协作原则**：
   - 可以组合使用不同类型的工具（如先查询数据，再搜索业务规则）
   - 同一工具的多次调用需要有实质性的差异（不同的查询条件、不同的数据表等）
   - 为向量数据库预留接口：未来可根据向量匹配结果决定调用策略

请根据用户的需求，智能地选择和使用工具完成任务。**重点：确保每次工具调用都有明确目的和价值！**"""
    
    def process_message(self, message: str, conversation_history: Optional[List[Dict]] = None,
                       system_message: str = None) -> Dict[str, Any]:
        """
        处理用户消息，支持多步骤工具调用（同步版本）

        Args:
            message: 用户消息
            conversation_history: 对话历史
            system_message: 系统消息（可选）

        Returns:
            包含回复和工具调用结果的字典
        """
        return self._process_message_sync(message, conversation_history, None, system_message)
    
    async def process_message_async(self, message: str, conversation_history: Optional[List[Dict]] = None, 
                       real_time_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        处理用户消息，支持多步骤工具调用（异步版本，支持实时回调）
        
        Args:
            message: 用户消息
            conversation_history: 对话历史
            real_time_callback: 实时回调函数，用于流式显示进度
                               格式: async def callback(event_type: str, data: dict)
            
        Returns:
            包含回复和工具调用结果的字典
        """
        return await self._process_message_async(message, conversation_history, real_time_callback)
    
    def _process_message_sync(self, message: str, conversation_history: Optional[List[Dict]] = None,
                       real_time_callback: Optional[callable] = None, system_message: str = None) -> Dict[str, Any]:
        """
        同步版本的消息处理方法
        """
        total_start_time = time.time()
        timing_info = {
            "total": 0,
            "llm_calls": [],
            "tool_calls": [],
            "iterations": 0
        }
        
        try:
            logger.info(f"🤖 [Agent] 开始处理用户消息: '{message[:100]}{'...' if len(message) > 100 else ''}'")
            
            # 构建初始消息列表
            setup_start = time.time()
            # 构建有效的系统提示词
            if system_message:
                # 如果有system_message，将其附加到系统提示词后面
                # 注意：self.system_prompt可能已经被VectorEnhancedAgent替换为复杂提示词
                effective_system_prompt = f"{self.system_prompt}\n\n{system_message}"
            else:
                # 否则使用默认的system_prompt
                effective_system_prompt = self.system_prompt
            messages = [SystemMessage(content=effective_system_prompt)]
            
            # 添加对话历史
            if conversation_history:
                logger.info(f"📚 [Agent] 加载对话历史: {len(conversation_history)} 条记录")
                for entry in conversation_history:
                    if entry.get("role") == "user":
                        messages.append(HumanMessage(content=entry["content"]))
                    elif entry.get("role") == "assistant":
                        messages.append(AIMessage(content=entry["content"]))
            else:
                logger.info(f"📚 [Agent] 无对话历史记录")
            
            # 添加当前用户消息
            messages.append(HumanMessage(content=message))
            setup_duration = time.time() - setup_start
            
            logger.info(f"⚙️ [Agent] 消息构建完成 (用时: {setup_duration:.3f}秒)")
            
            # 初始化追踪变量
            tool_calls_history = []
            iterations = 0
            current_messages = messages.copy()
            
            while iterations < self.max_iterations:
                iterations += 1
                iteration_start = time.time()
                logger.info(f"🔄 [Agent] 开始第 {iterations} 次迭代")
                
                # 调用LLM
                llm_start = time.time()
                response = self.llm.invoke(current_messages)
                response_text = response.content if hasattr(response, 'content') else str(response)
                llm_duration = time.time() - llm_start
                
                timing_info["llm_calls"].append({
                    "iteration": iterations,
                    "duration": llm_duration,
                    "input_messages": len(current_messages),
                    "output_length": len(response_text)
                })
                
                logger.info(f"🧠 [Agent] LLM响应完成 (第{iterations}次，用时: {llm_duration:.2f}秒，输出长度: {len(response_text)}字符)")
                
                # 检查是否需要调用工具
                tool_call = self._extract_tool_call(response_text)
                
                if tool_call:
                    # 执行工具调用
                    tool_start = time.time()
                    logger.info(f"🔧 [Agent] 调用工具: {tool_call.get('tool_name')} (参数: {tool_call.get('parameters')})")
                    
                    tool_result = self._execute_tool(tool_call)
                    tool_duration = time.time() - tool_start
                    
                    timing_info["tool_calls"].append({
                        "iteration": iterations,
                        "tool_name": tool_call.get('tool_name'),
                        "duration": tool_duration,
                        "success": tool_result.get('success', False),
                        "parameters": tool_call.get('parameters')
                    })
                    
                    logger.info(f"🔧 [Agent] 工具执行完成: {tool_call.get('tool_name')} (用时: {tool_duration:.2f}秒，成功: {tool_result.get('success', False)})")
                    
                    tool_calls_history.append({
                        "iteration": iterations,
                        "tool_call": tool_call,
                        "tool_result": tool_result,
                        "llm_response": response_text,
                        "timing": {
                            "llm_duration": llm_duration,
                            "tool_duration": tool_duration,
                            "iteration_duration": time.time() - iteration_start
                        }
                    })
                    
                    # 构建工具结果描述
                    tool_result_desc = self._format_tool_result(tool_call, tool_result)
                    
                    # 添加工具结果到对话历史
                    current_messages.append(AIMessage(content=response_text))
                    
                    # 🔧 通用提示：让LLM根据系统提示词的要求决定下一步
                    prompt = f"""工具执行结果：{tool_result_desc}\n\n请根据系统提示词的要求继续执行：\n1. 如果系统提示词要求进行多步骤分析，请继续执行后续步骤\n2. 如果需要调用其他工具来完成任务，请调用相应工具\n3. 如果任务已完成，请按照系统要求的格式输出最终结果\n\n严格按照系统提示词中的流程和要求执行，不要跳过任何步骤。"""
                    
                    current_messages.append(HumanMessage(content=prompt))
                    
                    iteration_duration = time.time() - iteration_start
                    logger.info(f"✅ [Agent] 第 {iterations} 次迭代完成 (总用时: {iteration_duration:.2f}秒)")
                    
                    # 如果工具执行失败，可能需要重试或使用其他方法
                    if not tool_result['success'] and self._should_retry_or_alternative(tool_call, tool_result):
                        continue
                        
                else:
                    # 没有工具调用，说明是最终回答
                    iteration_duration = time.time() - iteration_start
                    total_duration = time.time() - total_start_time
                    timing_info["total"] = total_duration
                    timing_info["iterations"] = iterations
                    
                    logger.info(f"🎯 [Agent] 处理完成，无需工具调用 (第{iterations}次迭代用时: {iteration_duration:.2f}秒，总用时: {total_duration:.2f}秒)")
                    
                    return {
                        "response": response_text,
                        "tool_used": len(tool_calls_history) > 0,
                        "tool_calls_history": tool_calls_history,
                        "iterations": iterations,
                        "final_response": response_text,
                        "timing": timing_info
                    }
            
            # 达到最大迭代次数
            total_duration = time.time() - total_start_time
            timing_info["total"] = total_duration
            timing_info["iterations"] = iterations
            
            logger.warning(f"⏰ [Agent] 达到最大迭代次数限制 ({self.max_iterations}次，总用时: {total_duration:.2f}秒)")
            
            final_response = self._generate_timeout_response(message, tool_calls_history)
            return {
                "response": final_response,
                "tool_used": len(tool_calls_history) > 0,
                "tool_calls_history": tool_calls_history,
                "iterations": iterations,
                "timeout": True,
                "final_response": final_response,
                "timing": timing_info
            }
                
        except Exception as e:
            total_duration = time.time() - total_start_time
            timing_info["total"] = total_duration
            
            logger.error(f"💥 [Agent] 处理消息时发生错误: {str(e)} (总用时: {total_duration:.2f}秒)")
            
            return {
                "response": f"处理消息时发生错误: {str(e)}",
                "tool_used": len(tool_calls_history) > 0 if 'tool_calls_history' in locals() else False,
                "tool_calls_history": tool_calls_history if 'tool_calls_history' in locals() else [],
                "iterations": iterations if 'iterations' in locals() else 0,
                "error": str(e),
                "timing": timing_info
            }
    
    async def _process_message_async(self, message: str, conversation_history: Optional[List[Dict]] = None, 
                       real_time_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        异步版本的消息处理方法，支持实时回调
        """
        import asyncio
        from concurrent.futures import ThreadPoolExecutor
        
        total_start_time = time.time()
        timing_info = {
            "total": 0,
            "llm_calls": [],
            "tool_calls": [],
            "iterations": 0
        }
        
        try:
            logger.info(f"🤖 [Agent] 开始处理用户消息: '{message[:100]}{'...' if len(message) > 100 else ''}'")
            
            # 构建初始消息列表
            setup_start = time.time()
            messages = [SystemMessage(content=self.system_prompt)]
            
            # 添加对话历史
            if conversation_history:
                logger.info(f"📚 [Agent] 加载对话历史: {len(conversation_history)} 条记录")
                for entry in conversation_history:
                    if entry.get("role") == "user":
                        messages.append(HumanMessage(content=entry["content"]))
                    elif entry.get("role") == "assistant":
                        messages.append(AIMessage(content=entry["content"]))
            else:
                logger.info(f"📚 [Agent] 无对话历史记录")
            
            # 添加当前用户消息
            messages.append(HumanMessage(content=message))
            setup_duration = time.time() - setup_start
            
            logger.info(f"⚙️ [Agent] 消息构建完成 (用时: {setup_duration:.3f}秒)")
            
            # 初始化追踪变量
            tool_calls_history = []
            iterations = 0
            current_messages = messages.copy()
            
            # 创建线程池用于异步执行同步操作
            executor = ThreadPoolExecutor(max_workers=2)
            
            while iterations < self.max_iterations:
                iterations += 1
                iteration_start = time.time()
                logger.info(f"🔄 [Agent] 开始第 {iterations} 次迭代")
                
                # 🆕 回调：迭代开始
                if real_time_callback:
                    try:
                        await real_time_callback("iteration_start", {
                            "iteration": iterations,
                            "total_iterations": self.max_iterations
                        })
                    except Exception as e:
                        logger.warning(f"⚠️ [Agent] 回调函数执行失败 (iteration_start): {e}")
                
                # 短暂延迟确保回调有时间处理
                await asyncio.sleep(0.1)
                
                # 调用LLM
                llm_start = time.time()
                
                # 🆕 回调：LLM调用开始
                if real_time_callback:
                    try:
                        await real_time_callback("llm_start", {
                            "iteration": iterations,
                            "input_messages": len(current_messages)
                        })
                    except Exception as e:
                        logger.warning(f"⚠️ [Agent] 回调函数执行失败 (llm_start): {e}")
                
                # 短暂延迟确保回调有时间处理
                await asyncio.sleep(0.1)
                
                # 🔧 关键修复：将同步LLM调用包装为异步
                def _call_llm():
                    return self.llm.invoke(current_messages)
                
                # 使用线程池异步执行LLM调用
                response = await asyncio.get_event_loop().run_in_executor(executor, _call_llm)
                response_text = response.content if hasattr(response, 'content') else str(response)
                llm_duration = time.time() - llm_start
                
                timing_info["llm_calls"].append({
                    "iteration": iterations,
                    "duration": llm_duration,
                    "input_messages": len(current_messages),
                    "output_length": len(response_text)
                })
                
                # 🆕 回调：LLM调用完成
                if real_time_callback:
                    try:
                        await real_time_callback("llm_complete", {
                            "iteration": iterations,
                            "duration": llm_duration,
                            "output_length": len(response_text),
                            "response_preview": response_text[:100]
                        })
                    except Exception as e:
                        logger.warning(f"⚠️ [Agent] 回调函数执行失败 (llm_complete): {e}")
                
                # 短暂延迟确保回调有时间处理
                await asyncio.sleep(0.1)
                
                logger.info(f"🧠 [Agent] LLM响应完成 (第{iterations}次，用时: {llm_duration:.2f}秒，输出长度: {len(response_text)}字符)")
                
                # 检查是否需要调用工具
                tool_call = self._extract_tool_call(response_text)
                
                if tool_call:
                    # 执行工具调用
                    tool_start = time.time()
                    logger.info(f"🔧 [Agent] 调用工具: {tool_call.get('tool_name')} (参数: {tool_call.get('parameters')})")
                    
                    # 🆕 回调：工具调用开始
                    if real_time_callback:
                        try:
                            await real_time_callback("tool_start", {
                                "iteration": iterations,
                                "tool_name": tool_call.get('tool_name'),
                                "parameters": tool_call.get('parameters')
                            })
                        except Exception as e:
                            logger.warning(f"⚠️ [Agent] 回调函数执行失败 (tool_start): {e}")
                    
                    # 短暂延迟确保回调有时间处理
                    await asyncio.sleep(0.1)
                    
                    # 🔧 关键修复：将同步工具调用包装为异步
                    def _call_tool():
                        return self._execute_tool(tool_call)
                    
                    # 使用线程池异步执行工具调用
                    tool_result = await asyncio.get_event_loop().run_in_executor(executor, _call_tool)
                    tool_duration = time.time() - tool_start
                    
                    # 🆕 回调：工具调用完成
                    if real_time_callback:
                        try:
                            await real_time_callback("tool_complete", {
                                "iteration": iterations,
                                "tool_name": tool_call.get('tool_name'),
                                "success": tool_result.get('success', False),
                                "duration": tool_duration,
                                "result": tool_result.get('result'),
                                "error": tool_result.get('error')
                            })
                        except Exception as e:
                            logger.warning(f"⚠️ [Agent] 回调函数执行失败 (tool_complete): {e}")
                    
                    # 短暂延迟确保回调有时间处理
                    await asyncio.sleep(0.1)
                    
                    timing_info["tool_calls"].append({
                        "iteration": iterations,
                        "tool_name": tool_call.get('tool_name'),
                        "duration": tool_duration,
                        "success": tool_result.get('success', False),
                        "parameters": tool_call.get('parameters')
                    })
                    
                    logger.info(f"🔧 [Agent] 工具执行完成: {tool_call.get('tool_name')} (用时: {tool_duration:.2f}秒，成功: {tool_result.get('success', False)})")
                    
                    tool_calls_history.append({
                        "iteration": iterations,
                        "tool_call": tool_call,
                        "tool_result": tool_result,
                        "llm_response": response_text,
                        "timing": {
                            "llm_duration": llm_duration,
                            "tool_duration": tool_duration,
                            "iteration_duration": time.time() - iteration_start
                        }
                    })
                    
                    # 构建工具结果描述
                    tool_result_desc = self._format_tool_result(tool_call, tool_result)
                    
                    # 添加工具结果到对话历史
                    current_messages.append(AIMessage(content=response_text))
                    
                    # 🔧 通用提示：让LLM根据系统提示词的要求决定下一步
                    prompt = f"""工具执行结果：{tool_result_desc}\n\n请根据系统提示词的要求继续执行：\n1. 如果系统提示词要求进行多步骤分析，请继续执行后续步骤\n2. 如果需要调用其他工具来完成任务，请调用相应工具\n3. 如果任务已完成，请按照系统要求的格式输出最终结果\n\n严格按照系统提示词中的流程和要求执行，不要跳过任何步骤。"""
                    
                    current_messages.append(HumanMessage(content=prompt))
                    
                    iteration_duration = time.time() - iteration_start
                    
                    # 🆕 回调：迭代完成
                    if real_time_callback:
                        try:
                            await real_time_callback("iteration_complete", {
                                "iteration": iterations,
                                "duration": iteration_duration,
                                "tool_used": True,
                                "tool_success": tool_result.get('success', False)
                            })
                        except Exception as e:
                            logger.warning(f"⚠️ [Agent] 回调函数执行失败 (iteration_complete): {e}")
                    
                    logger.info(f"✅ [Agent] 第 {iterations} 次迭代完成 (总用时: {iteration_duration:.2f}秒)")
                    
                    # 如果工具执行失败，可能需要重试或使用其他方法
                    if not tool_result['success'] and self._should_retry_or_alternative(tool_call, tool_result):
                        continue
                        
                else:
                    # 没有工具调用，说明是最终回答
                    iteration_duration = time.time() - iteration_start
                    total_duration = time.time() - total_start_time
                    timing_info["total"] = total_duration
                    timing_info["iterations"] = iterations
                    
                    # 🆕 回调：迭代完成（无工具调用）
                    if real_time_callback:
                        try:
                            await real_time_callback("iteration_complete", {
                                "iteration": iterations,
                                "duration": iteration_duration,
                                "tool_used": False,
                                "final_response": True
                            })
                        except Exception as e:
                            logger.warning(f"⚠️ [Agent] 回调函数执行失败 (iteration_complete): {e}")
                    
                    logger.info(f"🎯 [Agent] 处理完成，无需工具调用 (第{iterations}次迭代用时: {iteration_duration:.2f}秒，总用时: {total_duration:.2f}秒)")
                    
                    # 清理线程池
                    executor.shutdown(wait=False)
                    
                    return {
                        "response": response_text,
                        "tool_used": len(tool_calls_history) > 0,
                        "tool_calls_history": tool_calls_history,
                        "iterations": iterations,
                        "final_response": response_text,
                        "timing": timing_info
                    }
            
            # 达到最大迭代次数
            total_duration = time.time() - total_start_time
            timing_info["total"] = total_duration
            timing_info["iterations"] = iterations
            
            logger.warning(f"⏰ [Agent] 达到最大迭代次数限制 ({self.max_iterations}次，总用时: {total_duration:.2f}秒)")
            
            # 清理线程池
            executor.shutdown(wait=False)
            
            final_response = self._generate_timeout_response(message, tool_calls_history)
            return {
                "response": final_response,
                "tool_used": len(tool_calls_history) > 0,
                "tool_calls_history": tool_calls_history,
                "iterations": iterations,
                "timeout": True,
                "final_response": final_response,
                "timing": timing_info
            }
                
        except Exception as e:
            total_duration = time.time() - total_start_time
            timing_info["total"] = total_duration
            
            logger.error(f"💥 [Agent] 处理消息时发生错误: {str(e)} (总用时: {total_duration:.2f}秒)")
            
            # 清理线程池
            if 'executor' in locals():
                executor.shutdown(wait=False)
            
            return {
                "response": f"处理消息时发生错误: {str(e)}",
                "tool_used": len(tool_calls_history) > 0 if 'tool_calls_history' in locals() else False,
                "tool_calls_history": tool_calls_history if 'tool_calls_history' in locals() else [],
                "iterations": iterations if 'iterations' in locals() else 0,
                "error": str(e),
                "timing": timing_info
            }
    
    def _extract_tool_call(self, response_text: str) -> Optional[Dict[str, Any]]:
        """从回复中提取工具调用"""
        try:
            # 查找JSON代码块
            json_pattern = r'```json\s*(\{.*?\})\s*```'
            matches = re.findall(json_pattern, response_text, re.DOTALL)
            
            if matches:
                tool_call_str = matches[0]
                tool_call = json.loads(tool_call_str)
                
                # 验证工具调用格式
                if "tool_name" in tool_call and "parameters" in tool_call:
                    return tool_call
            
            return None
            
        except (json.JSONDecodeError, Exception):
            return None
    
    def _execute_tool(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具调用"""
        start_time = time.time()
        try:
            tool_name = tool_call["tool_name"]
            parameters = tool_call["parameters"]
            
            logger.info(f"🔧 [工具调用] 准备执行工具: {tool_name}")
            logger.info(f"🔧 [工具调用] 传递参数: {parameters}")
            
            if tool_name not in self.tool_map:
                duration = time.time() - start_time
                error_msg = f"未知工具: {tool_name}"
                logger.error(f"❌ [工具调用] {error_msg} (用时: {duration:.3f}秒)")
                return {
                    "success": False,
                    "error": error_msg,
                    "result": None,
                    "timing": {"duration": duration}
                }
            
            tool = self.tool_map[tool_name]
            
            # 实际执行工具
            execute_start = time.time()
            result = tool.call(**parameters)
            execute_duration = time.time() - execute_start
            
            total_duration = time.time() - start_time
            
            if result.success:
                logger.info(f"✅ [工具调用] 工具执行成功: {tool_name} (执行用时: {execute_duration:.3f}秒，总用时: {total_duration:.3f}秒)")
            else:
                logger.error(f"❌ [工具调用] 工具执行失败: {tool_name}, 错误: {result.error} (执行用时: {execute_duration:.3f}秒，总用时: {total_duration:.3f}秒)")
            
            return {
                "success": result.success,
                "result": result.result,
                "error": result.error,
                "timing": {
                    "duration": total_duration,
                    "execute_duration": execute_duration,
                    "tool_name": tool_name
                }
            }
            
        except Exception as e:
            total_duration = time.time() - start_time
            error_msg = f"工具执行错误: {str(e)}"
            logger.error(f"💥 [工具调用] {error_msg} (总用时: {total_duration:.3f}秒)")
            return {
                "success": False,
                "error": error_msg,
                "result": None,
                "timing": {"duration": total_duration}
            }
    
    def _format_tool_result(self, tool_call: Dict[str, Any], tool_result: Dict[str, Any]) -> str:
        """格式化工具执行结果"""
        if tool_result['success']:
            result_str = f"✅ 工具 {tool_call['tool_name']} 执行成功\n"
            result_str += f"结果: {tool_result['result']}"
            
            # 特殊处理SQL生成器结果
            if tool_call['tool_name'] == 'sql_generator' and tool_result['result']:
                if isinstance(tool_result['result'], dict) and 'sql' in tool_result['result']:
                    generated_sql = tool_result['result']['sql']
                    result_str += f"\n生成的SQL: {generated_sql}"
                    result_str += f"\n\n💡 建议：现在可以使用sql_executor工具执行这个SQL查询"
            
            return result_str
        else:
            return f"❌ 工具 {tool_call['tool_name']} 执行失败\n错误: {tool_result.get('error', '未知错误')}"
    
    def _should_retry_or_alternative(self, tool_call: Dict[str, Any], tool_result: Dict[str, Any]) -> bool:
        """判断是否应该重试或尝试替代方案"""
        # 这里可以添加更复杂的重试逻辑
        # 比如网络错误可以重试，参数错误不应该重试
        error_msg = tool_result.get('error', '').lower()
        
        # 网络相关错误可以重试
        if any(keyword in error_msg for keyword in ['network', 'connection', 'timeout', '网络', '连接', '超时']):
            return True
            
        return False
    
    def _generate_timeout_response(self, original_message: str, tool_calls_history: List[Dict]) -> str:
        """生成超时响应"""
        if not tool_calls_history:
            return "抱歉，我无法处理您的请求。"
        
        last_tool_call = tool_calls_history[-1]
        if last_tool_call['tool_result']['success']:
            result = last_tool_call['tool_result']['result']
            return f"基于执行的工具结果，我得到了以下信息：{result}"
        else:
            return f"抱歉，在处理您的请求时遇到了问题：{last_tool_call['tool_result'].get('error', '未知错误')}"
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return [tool.to_dict() for tool in self.tools]
    
    def _should_continue_calling_tools(self, tool_calls_history: List[Dict], current_question: str) -> bool:
        """
        智能分析是否应该继续调用工具
        
        Args:
            tool_calls_history: 工具调用历史
            current_question: 当前问题
            
        Returns:
            是否应该继续调用工具
        """
        if not tool_calls_history:
            return True
        
        # 获取最近的工具调用
        last_call = tool_calls_history[-1]
        last_tool_name = last_call["tool_call"]["tool_name"]
        last_success = last_call["tool_result"]["success"]
        
        # 如果最后一次调用失败，可能需要重试或换工具
        if not last_success:
            return True
        
        # 统计同类工具调用次数
        same_tool_calls = [call for call in tool_calls_history 
                          if call["tool_call"]["tool_name"] == last_tool_name]
        
        # 如果同一工具已被调用多次，需要更严格的判断
        if len(same_tool_calls) >= 1:
            # TODO: 这里可以集成向量数据库来判断
            # 目前简单策略：同一工具最多调用1次
            return False
        
        # 如果启用了向量数据库指导
        if self.enable_vector_guidance:
            # TODO: 集成向量数据库分析
            # 根据向量匹配结果决定是否需要进一步查询
            pass
        
        return True
    
    def _analyze_tool_call_necessity(self, user_message: str, tool_calls_history: List[Dict]) -> Dict[str, Any]:
        """
        分析工具调用的必要性（为向量数据库集成预留）
        
        Args:
            user_message: 用户消息
            tool_calls_history: 工具调用历史
            
        Returns:
            分析结果
        """
        analysis = {
            "should_call": True,
            "recommended_tool": None,
            "reason": "",
            "vector_guidance": None
        }
        
        # TODO: 在这里集成向量数据库逻辑
        # 1. 将用户问题向量化
        # 2. 匹配最相关的数据表和字段
        # 3. 分析已有的查询结果是否覆盖了必要信息
        # 4. 推荐下一步的查询策略
        
        if self.enable_vector_guidance and self.vector_db_config:
            # 向量数据库指导逻辑
            pass
        
        return analysis 