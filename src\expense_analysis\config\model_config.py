"""
费用分析专用模型配置
支持从环境变量读取配置，便于不同环境切换
"""

import os
from dotenv import load_dotenv

# 根据环境变量选择配置文件
expense_env = os.getenv('EXPENSE_ENV', 'dev')  # 默认开发环境
config_file = f'.env.expense.{expense_env}'
expense_config_path = os.path.join(os.path.dirname(__file__), '..', config_file)

# 如果环境特定配置文件不存在，使用默认配置
if not os.path.exists(expense_config_path):
    expense_config_path = os.path.join(os.path.dirname(__file__), '..', '.env.expense')

load_dotenv(expense_config_path)
print(f"📁 加载费用分析配置: {os.path.basename(expense_config_path)} (EXPENSE_ENV={expense_env})")

# 费用分析专用LLM配置 - 从环境变量读取
EXPENSE_ANALYSIS_LLM_CONFIG = {
    # 模型名称
    "model_name": os.getenv("EXPENSE_ANALYSIS_MODEL_NAME", "Qwen2.5-72B-Instruct"),

    # 模型服务配置
    "api_url": os.getenv("EXPENSE_ANALYSIS_API_URL", "http://ai.ai.iot.chinamobile.com/imaas/v1/chat/completions"),
    "api_key": os.getenv("EXPENSE_ANALYSIS_API_KEY", "sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ"),

    # 分类任务优化参数
    "temperature": float(os.getenv("EXPENSE_ANALYSIS_TEMPERATURE", "0.1")),      # 低温度，提高一致性
    "max_tokens": int(os.getenv("EXPENSE_ANALYSIS_MAX_TOKENS", "2000")),        # 适中的token数量
    "timeout": int(os.getenv("EXPENSE_ANALYSIS_TIMEOUT", "300")),               # 5分钟超时

    # 性能优化
    "preserve_think_tags": os.getenv("EXPENSE_ANALYSIS_PRESERVE_THINK_TAGS", "False").lower() == "true",   # 禁用思考标签
    "stream": os.getenv("EXPENSE_ANALYSIS_STREAM", "False").lower() == "true",                            # 禁用流式输出
}

def get_model_config():
    """
    获取模型配置

    Returns:
        dict: 模型配置
    """
    return EXPENSE_ANALYSIS_LLM_CONFIG.copy()

if __name__ == "__main__":
    config = get_model_config()
    print("🤖 费用分析模型配置")
    print("=" * 40)
    print(f"📝 模型: {config['model_name']}")
    print(f"🌡️ 温度: {config['temperature']}")
    print(f"⏱️ 超时: {config['timeout']}秒")
