# 配置管理指南

## 📋 概述

本项目采用分层配置管理策略，支持多环境部署和灵活的配置切换。

## 🏗️ 配置架构

### 主配置系统
- **位置**: `src/config/`
- **文件**: `settings.py`, `database_config.py`
- **作用**: 管理主应用的配置（LLM、数据库、服务器等）
- **配置源**: `.env` 文件 + 环境变量

### 费用分析模块配置
- **位置**: `src/expense_analysis/config/`
- **文件**: `model_config.py`
- **作用**: 管理费用分析专用的模型配置
- **配置源**: `.env.expense.*` 文件 + 环境变量

## ⚙️ 配置文件说明

### 主配置文件
```
.env                    # 主环境配置
env.example            # 配置模板
```

### 费用分析配置文件
```
src/expense_analysis/
├── .env.expense.dev   # 开发环境配置
├── .env.expense.prod  # 生产环境配置
└── .env.expense       # 默认配置
```

## 🔄 环境切换

### 主应用
主应用配置通过修改 `.env` 文件来切换环境：

```bash
# 编辑主配置
vim .env

# 修改关键配置项
DEBUG=false                    # 生产环境关闭调试
LOG_LEVEL=WARNING             # 生产环境减少日志
MYSQL_HOST=prod-db-server     # 生产数据库
```

### 费用分析模块
费用分析模块支持通过环境变量动态切换：

```bash
# 开发环境（默认）
python src/expense_analysis/manage_api.py start

# 生产环境
EXPENSE_ENV=prod python src/expense_analysis/manage_api.py start

# 查看当前配置
EXPENSE_ENV=prod python src/expense_analysis/config/model_config.py
```

## 📊 配置对比

### 费用分析环境差异

| 配置项 | 开发环境 | 生产环境 | 说明 |
|--------|----------|----------|------|
| `EXPENSE_ANALYSIS_TEMPERATURE` | 0.3 | 0.05 | 生产环境更保守 |
| `EXPENSE_ANALYSIS_TIMEOUT` | 120 | 600 | 生产环境更长超时 |
| `EXPENSE_ANALYSIS_MAX_TOKENS` | 1000 | 3000 | 生产环境支持更复杂分析 |
| `EXPENSE_ANALYSIS_PRESERVE_THINK_TAGS` | true | false | 开发环境启用调试 |
| `EXPENSE_ANALYSIS_API_URL` | 测试服务器 | 生产服务器 | 不同环境不同服务 |

## 🔒 安全最佳实践

### 1. 敏感信息管理
- ✅ 使用环境变量存储API密钥
- ✅ 生产配置文件不提交到git
- ✅ 使用配置模板文件（.example）

### 2. 环境隔离
- ✅ 开发和生产使用不同的数据库
- ✅ 开发和生产使用不同的API服务
- ✅ 配置文件分离，避免误用

### 3. 部署建议
```bash
# 生产部署示例
export EXPENSE_ENV=prod
export EXPENSE_ANALYSIS_API_KEY=actual_prod_key
export MYSQL_PASSWORD=actual_prod_password
python src/expense_analysis/manage_api.py start
```

## 🛠️ 配置工具

### 查看当前配置
```bash
# 主应用配置
python -c "from src.config.settings import settings; print(f'Debug: {settings.debug}, Host: {settings.mysql_host}')"

# 费用分析配置
python src/expense_analysis/config/model_config.py
```

### 配置验证
```bash
# 验证数据库连接
python -c "from src.config.database_config import DatabaseConfig; print(DatabaseConfig.get_mysql_config())"

# 验证模型配置
python -c "from src.expense_analysis.config.model_config import get_model_config; import json; print(json.dumps(get_model_config(), indent=2))"
```

## 🚨 故障排除

### 常见问题

1. **配置文件未找到**
   ```bash
   # 检查文件是否存在
   ls -la src/expense_analysis/.env.expense.*
   ```

2. **环境变量未生效**
   ```bash
   # 检查环境变量
   echo $EXPENSE_ENV
   ```

3. **配置加载失败**
   ```bash
   # 查看详细错误信息
   python -c "from src.expense_analysis.config.model_config import get_model_config; get_model_config()"
   ```

## 📝 配置模板

### 创建新环境配置
```bash
# 复制现有配置
cp src/expense_analysis/.env.expense.dev src/expense_analysis/.env.expense.test

# 编辑新配置
vim src/expense_analysis/.env.expense.test

# 测试新配置
EXPENSE_ENV=test python src/expense_analysis/config/model_config.py
```

通过这套配置管理系统，可以轻松在不同环境间切换，确保开发、测试、生产环境的配置隔离和安全性。
